<p align="center"><img src="https://docmee.cn/favicons/favicon-32x32.png" alt="logo"/></p>
<h1 align="center">Docmee AiPPT</h1>
<p align="center">
  English | <a href="./README.md">简体中文</a>
</p>
<p align="center">
	<a href="https://veasion.github.io/AiPPT" target="_blank">🔗Demo</a>
	<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
	<a href="https://veasion.github.io/AiPPT/ppt2json.html" target="_blank">📝PPT to JSON</a>
	<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
	<a href="https://docmee.cn" target="_blank">🌏Official website</a>
	<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
	<a href="#-Business+cooperation">💬Business cooperation</a>
</p>





# 🤖 AI Generate PPT

Commercial level AI generated PPT project, including the following features:

* AI generated PPT
* PPT parsed into JSON
* JSON re-rendered as PPT



# ✨ AiPPT

Demo: https://veasion.github.io/AiPPT

[Demo Video](https://metasign-public.oss-cn-shanghai.aliyuncs.com/github/aippt.mp4)

https://github.com/veasion/aippt/assets/24989778/24d5654b-09f3-4554-a732-dbffc1073a1d



# ✨ PPT to JSON

Support uploading PPT and rendering, online editing, and download the edited PPT file.

Demo: https://veasion.github.io/AiPPT/ppt2json.html




<img width="800" src="https://metasign-public.oss-cn-shanghai.aliyuncs.com/github/ppt2json.png" style="border:1px solid #ccc">



# 🤝 Business cooperation

We have developed a commercially available aippt software that supports proxy and private deployment for the above technology!

Our advantages include supporting customized industry solutions, supporting complex PPT analysis and rendering such as native charts and animations, supporting user-defined templates, leading technology solutions, and the lowest prices in the industry.

Official website (Open API)：
https://docmee.cn

Open platform (API / UI)：
https://docmee.cn/open-platform


WeChat group qrcode：

![qrcode](https://metasign-public.oss-cn-shanghai.aliyuncs.com/github/contact_me_qr.png)



# 🌟 Star History


<picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=veasion/aippt&type=Date&theme=dark" />
    <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=veasion/aippt&type=Date" />
    <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=veasion/aippt&type=Date" />
</picture>
