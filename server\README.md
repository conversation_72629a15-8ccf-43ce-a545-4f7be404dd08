# 关于服务端代码

目前该项目仅开源了前端PPT渲染引擎代码，服务端代码暂未开放

可以先通过开放平台API/UI方式接入，后续我们会在B站上录制讲解课程以及开放相关技术路线，敬请期待！



我们支持私有化部署

官网在线体验（开放API）：https://docmee.cn

开放平台（API/UI 接入）： https://docmee.cn/open-platform



API SDK DEMO：

* iframe demo

  https://github.com/veasion/aippt-ui-iframe

* js demo

  https://github.com/veasion/aippt

* vue demo

  https://github.com/veasion/aippt-vue

* react demo

  https://github.com/veasion/aippt-react

* java demo

  https://github.com/veasion/aippt-api-java-demo

* python demo

  https://github.com/veasion/aippt-api-python-demo

* go demo

  https://github.com/veasion/aippt-api-go-demo

* .net / c# demo

  https://github.com/veasion/aippt-api-dotnet-demo

* php demo

  https://github.com/veasion/aippt-api-php-demo

* c++ demo

  https://github.com/veasion/aippt-api-cpp-demo



进群交流（文多多AiPPT技术交流群）：

![qrcode](https://metasign-public.oss-cn-shanghai.aliyuncs.com/github/contact_me_qr.png)
