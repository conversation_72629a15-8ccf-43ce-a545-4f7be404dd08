/* eslint-disable no-fallthrough */
/* eslint-disable no-case-declarations */
const geometryMap = {"curvedDownArrow":{"guides":["maxAdj2:*/ 50000 w ss","a2:pin 0 adj2 maxAdj2","a1:pin 0 adj1 100000","th:*/ ss a1 100000","aw:*/ ss a2 100000","q1:+/ th aw 4","wR:+- wd2 0 q1","q7:*/ wR 2 1","q8:*/ q7 q7 1","q9:*/ th th 1","q10:+- q8 0 q9","q11:sqrt q10","idy:*/ q11 h q7","maxAdj3:*/ 100000 idy ss","a3:pin 0 adj3 maxAdj3","ah:*/ ss adj3 100000","x3:+- wR th 0","q2:*/ h h 1","q3:*/ ah ah 1","q4:+- q2 0 q3","q5:sqrt q4","dx:*/ q5 wR h","x5:+- wR dx 0","x7:+- x3 dx 0","q6:+- aw 0 th","dh:*/ q6 1 2","x4:+- x5 0 dh","x8:+- x7 dh 0","aw2:*/ aw 1 2","x6:+- r 0 aw2","y1:+- b 0 ah","swAng:at2 ah dx","mswAng:+- 0 0 swAng","iy:+- b 0 idy","ix:+/ wR x3 2","q12:*/ th 1 2","dang2:at2 idy q12","stAng:+- 3cd4 swAng 0","stAng2:+- 3cd4 0 dang2","swAng2:+- dang2 0 cd4","swAng3:+- cd4 dang2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x6 b L x4 y1 L x5 y1 A wR h stAng mswAng L x3 t A wR h 3cd4 swAng L x8 y1 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M ix iy A wR h stAng2 swAng2 L l b A wR h cd2 swAng3 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M ix iy A wR h stAng2 swAng2 L l b A wR h cd2 cd4 L x3 t A wR h 3cd4 swAng L x8 y1 L x6 b L x4 y1 L x5 y1 A wR h stAng mswAng","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 50000","adj3:val 25000"]},"quadArrowCallout":{"guides":["a2:pin 0 adj2 50000","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:+- 50000 0 a2","a3:pin 0 adj3 maxAdj3","q2:*/ a3 2 1","maxAdj4:+- 100000 0 q2","a4:pin a1 adj4 maxAdj4","dx2:*/ ss a2 100000","dx3:*/ ss a1 200000","ah:*/ ss a3 100000","dx1:*/ w a4 200000","dy1:*/ h a4 200000","x8:+- r 0 ah","x2:+- hc 0 dx1","x7:+- hc dx1 0","x3:+- hc 0 dx2","x6:+- hc dx2 0","x4:+- hc 0 dx3","x5:+- hc dx3 0","y8:+- b 0 ah","y2:+- vc 0 dy1","y7:+- vc dy1 0","y3:+- vc 0 dx2","y6:+- vc dx2 0","y4:+- vc 0 dx3","y5:+- vc dx3 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L ah y3 L ah y4 L x2 y4 L x2 y2 L x4 y2 L x4 ah L x3 ah L hc t L x6 ah L x5 ah L x5 y2 L x7 y2 L x7 y4 L x8 y4 L x8 y3 L r vc L x8 y6 L x8 y5 L x7 y5 L x7 y7 L x5 y7 L x5 y8 L x6 y8 L hc b L x3 y8 L x4 y8 L x4 y7 L x2 y7 L x2 y5 L ah y5 L ah y6 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18515","adj2:val 18515","adj3:val 18515","adj4:val 48123"]},"funnel":{"guides":["d:*/ ss 1 20","rw2:+- wd2 0 d","rh2:+- hd4 0 d","t1:cos wd2 480000","t2:sin hd4 480000","da:at2 t1 t2","2da:*/ da 2 1","stAng1:+- cd2 0 da","swAng1:+- cd2 2da 0","swAng3:+- cd2 0 2da","rw3:*/ wd2 1 4","rh3:*/ hd4 1 4","ct1:cos hd4 stAng1","st1:sin wd2 stAng1","m1:mod ct1 st1 0","n1:*/ wd2 hd4 m1","dx1:cos n1 stAng1","dy1:sin n1 stAng1","x1:+- hc dx1 0","y1:+- hd4 dy1 0","ct3:cos rh3 da","st3:sin rw3 da","m3:mod ct3 st3 0","n3:*/ rw3 rh3 m3","dx3:cos n3 da","dy3:sin n3 da","x3:+- hc dx3 0","vc3:+- b 0 rh3","y2:+- vc3 dy3 0","x2:+- wd2 0 rw2","cd:*/ cd2 2 1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 A wd2 hd4 stAng1 swAng1 L x3 y2 A rw3 rh3 da swAng3 Z M x2 hd4 A rw2 rh2 cd2 -21600000 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"doubleWave":{"guides":["a1:pin 0 adj1 12500","a2:pin -10000 adj2 10000","y1:*/ h a1 100000","dy2:*/ y1 10 3","y2:+- y1 0 dy2","y3:+- y1 dy2 0","y4:+- b 0 y1","y5:+- y4 0 dy2","y6:+- y4 dy2 0","dx1:*/ w a2 100000","of2:*/ w a2 50000","x1:abs dx1","dx2:?: of2 0 of2","x2:+- l 0 dx2","dx8:?: of2 of2 0","x8:+- r 0 dx8","dx3:+/ dx2 x8 6","x3:+- x2 dx3 0","dx4:+/ dx2 x8 3","x4:+- x2 dx4 0","x5:+/ x2 x8 2","x6:+- x5 dx3 0","x7:+/ x6 x8 2","x9:+- l dx8 0","x15:+- r dx2 0","x10:+- x9 dx3 0","x11:+- x9 dx4 0","x12:+/ x9 x15 2","x13:+- x12 dx3 0","x14:+/ x13 x15 2","x16:+- r 0 x1","xAdj:+- hc dx1 0","il:max x2 x9","ir:min x8 x15","it:*/ h a1 50000","ib:+- b 0 it"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x2 y1 C x3 y2 x4 y3 x5 y1 C x6 y2 x7 y3 x8 y1 L x15 y4 C x14 y6 x13 y5 x12 y4 C x11 y6 x10 y5 x9 y4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 6250","adj2:val 0"]},"parallelogram":{"guides":["maxAdj:*/ 100000 w ss","a:pin 0 adj maxAdj","x1:*/ ss a 200000","x2:*/ ss a 100000","x6:+- r 0 x1","x5:+- r 0 x2","x3:*/ x5 1 2","x4:+- r 0 x3","il:*/ wd2 a maxAdj","q1:*/ 5 a maxAdj","q2:+/ 1 q1 12","il:*/ q2 w 1","it:*/ q2 h 1","ir:+- r 0 il","ib:+- b 0 it","q3:*/ h hc x2","y1:pin 0 q3 h","y2:+- b 0 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L x2 t L r t L x5 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"rightArrowCallout":{"guides":["maxAdj2:*/ 50000 h ss","a2:pin 0 adj2 maxAdj2","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:*/ 100000 w ss","a3:pin 0 adj3 maxAdj3","q2:*/ a3 ss w","maxAdj4:+- 100000 0 q2","a4:pin 0 adj4 maxAdj4","dy1:*/ ss a2 100000","dy2:*/ ss a1 200000","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc dy2 0","y4:+- vc dy1 0","dx3:*/ ss a3 100000","x3:+- r 0 dx3","x2:*/ w a4 100000","x1:*/ x2 1 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x2 t L x2 y2 L x3 y2 L x3 y1 L r vc L x3 y4 L x3 y3 L x2 y3 L x2 b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 64977"]},"actionButtonForwardNext":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g12 vc L g11 g9 L g11 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g12 vc L g11 g9 L g11 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g12 vc L g11 g10 L g11 g9 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"flowChartDisplay":{"guides":["x2:*/ w 5 6"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":6,"path":"M 0 3 L 1 0 L 5 0 A 1 3 3cd4 cd2 L 1 6 Z","stroked":true,"w":6,"windingRule":1}],"adjusts":[]},"plaqueTabs":{"guides":["md:mod w h 0","dx:*/ 1 md 20","y1:+- 0 b dx","x1:+- 0 r dx"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L dx t A dx dx 0 cd4 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 A dx dx 3cd4 cd4 L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r t L r dx A dx dx cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 b A dx dx cd2 cd4 L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"actionButtonInformation":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g11:+- hc 0 dx2","g13:*/ ss 3 4","g14:*/ g13 1 32","g17:*/ g13 5 16","g18:*/ g13 3 8","g19:*/ g13 13 32","g20:*/ g13 19 32","g22:*/ g13 11 16","g23:*/ g13 13 16","g24:*/ g13 7 8","g25:+- g9 g14 0","g28:+- g9 g17 0","g29:+- g9 g18 0","g30:+- g9 g23 0","g31:+- g9 g24 0","g32:+- g11 g17 0","g34:+- g11 g19 0","g35:+- g11 g20 0","g37:+- g11 g22 0","g38:*/ g13 3 32"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M hc g9 A dx2 dx2 3cd4 21600000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M hc g9 A dx2 dx2 3cd4 21600000 Z M hc g25 A g38 g38 3cd4 21600000 M g32 g28 L g32 g29 L g34 g29 L g34 g30 L g32 g30 L g32 g31 L g37 g31 L g37 g30 L g35 g30 L g35 g28 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"LIGHTEN","filled":true,"h":-1,"path":"M hc g25 A g38 g38 3cd4 21600000 M g32 g28 L g35 g28 L g35 g30 L g37 g30 L g37 g31 L g32 g31 L g32 g30 L g34 g30 L g34 g29 L g32 g29 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M hc g9 A dx2 dx2 3cd4 21600000 Z M hc g25 A g38 g38 3cd4 21600000 M g32 g28 L g35 g28 L g35 g30 L g37 g30 L g37 g31 L g32 g31 L g32 g30 L g34 g30 L g34 g29 L g32 g29 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"flowChartOfflineStorage":{"guides":["x4:*/ w 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":2,"path":"M 0 0 L 2 0 L 1 2 Z","stroked":false,"w":2,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":5,"path":"M 2 4 L 3 4","stroked":true,"w":5,"windingRule":1},{"extrusionOk":true,"fill":"NONE","filled":false,"h":2,"path":"M 0 0 L 2 0 L 1 2 Z","stroked":true,"w":2,"windingRule":1}],"adjusts":[]},"upArrow":{"guides":["maxAdj2:*/ 100000 h ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","dy2:*/ ss a2 100000","y2:+- t dy2 0","dx1:*/ w a1 200000","x1:+- hc 0 dx1","x2:+- hc dx1 0","dy1:*/ x1 dy2 wd2","y1:+- y2  0 dy1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L hc t L r y2 L x2 y2 L x2 b L x1 b L x1 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"chartStar":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":10,"path":"M 0 0 L 10 10 M 0 10 L 10 0 M 5 0 L 5 10","stroked":true,"w":10,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":10,"path":"M 0 0 L 0 10 L 10 10 L 10 0 Z","stroked":false,"w":10,"windingRule":1}],"adjusts":[]},"actionButtonBlank":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"corner":{"guides":["maxAdj1:*/ 100000 h ss","maxAdj2:*/ 100000 w ss","a1:pin 0 adj1 maxAdj1","a2:pin 0 adj2 maxAdj2","x1:*/ ss a2 100000","dy1:*/ ss a1 100000","y1:+- b 0 dy1","cx1:*/ x1 1 2","cy1:+/ y1 b 2","d:+- w 0 h","it:?: d y1 t","ir:?: d r x1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x1 t L x1 y1 L r y1 L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"cloudCallout":{"guides":["dxPos:*/ w adj1 100000","dyPos:*/ h adj2 100000","xPos:+- hc dxPos 0","yPos:+- vc dyPos 0","ht:cat2 hd2 dxPos dyPos","wt:sat2 wd2 dxPos dyPos","g2:cat2 wd2 ht wt","g3:sat2 hd2 ht wt","g4:+- hc g2 0","g5:+- vc g3 0","g6:+- g4 0 xPos","g7:+- g5 0 yPos","g8:mod g6 g7 0","g9:*/ ss 6600 21600","g10:+- g8 0 g9","g11:*/ g10 1 3","g12:*/ ss 1800 21600","g13:+- g11 g12 0","g14:*/ g13 g6 g8","g15:*/ g13 g7 g8","g16:+- g14 xPos 0","g17:+- g15 yPos 0","g18:*/ ss 4800 21600","g19:*/ g11 2 1","g20:+- g18 g19 0","g21:*/ g20 g6 g8","g22:*/ g20 g7 g8","g23:+- g21 xPos 0","g24:+- g22 yPos 0","g25:*/ ss 1200 21600","g26:*/ ss 600 21600","x23:+- xPos g26 0","x24:+- g16 g25 0","x25:+- g23 g12 0","il:*/ w 2977 21600","it:*/ h 3262 21600","ir:*/ w 17087 21600","ib:*/ h 17337 21600","g27:*/ w 67 21600","g28:*/ h 21577 21600","g29:*/ w 21582 21600","g30:*/ h 1235 21600","pang:at2 dxPos dyPos"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":43200,"path":"M 3900 14370 A 6753 9190 -11429249 7426832 A 5333 7267 -8646143 5396714 A 4365 5945 -8748475 5983381 A 4857 6595 -7859164 7034504 A 5333 7273 -4722533 6541615 A 6775 9220 -2776035 7816140 A 5785 7867 37501 6842000 A 6752 9215 1347096 6910353 A 7720 10543 3974558 4542661 A 4360 5918 -16496525 8804134 A 4345 5945 -14809710 9151131 Z","stroked":true,"w":43200,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x23 yPos A g26 g26 0 21600000 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x24 g17 A g25 g25 0 21600000 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x25 g24 A g12 g12 0 21600000 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":43200,"path":"M 4693 26177 A 4345 5945 5204520 1585770 M 6928 34899 A 4360 5918 4416628 686848 M 16478 39090 A 6752 9215 8257449 844866 M 28827 34751 A 6752 9215 387196 959901 M 34129 22954 A 5785 7867 -4217541 4255042 M 41798 15354 A 5333 7273 1819082 1665090 M 38324 5426 A 4857 6595 -824660 891534 M 29078 3952 A 4857 6595 -8950887 1091722 M 22141 4720 A 4365 5945 -9809656 1061181 M 14000 5192 A 6753 9190 -4002417 739161 M 4127 15789 A 6753 9190 9459261 711490","stroked":true,"w":43200,"windingRule":1}],"adjusts":["adj1:val -20833","adj2:val 62500"]},"horizontalScroll":{"guides":["a:pin 0 adj 25000","ch:*/ ss a 100000","ch2:*/ ch 1 2","ch4:*/ ch 1 4","y3:+- ch ch2 0","y4:+- ch ch 0","y6:+- b 0 ch","y7:+- b 0 ch2","y5:+- y6 0 ch2","x3:+- r 0 ch","x4:+- r 0 ch2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r ch2 A ch2 ch2 0 cd4 L x4 ch2 A ch4 ch4 0 cd2 L x3 ch L ch2 ch A ch2 ch2 3cd4 -5400000 L l y7 A ch2 ch2 cd2 -10800000 L ch y6 L x4 y6 A ch2 ch2 cd4 -5400000 Z M ch2 y4 A ch2 ch2 cd4 -5400000 A ch4 ch4 0 -10800000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M ch2 y4 A ch2 ch2 cd4 -5400000 A ch4 ch4 0 -10800000 Z M x4 ch A ch2 ch2 cd4 -16200000 A ch4 ch4 cd2 -10800000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l y3 A ch2 ch2 cd2 cd4 L x3 ch L x3 ch2 A ch2 ch2 cd2 cd2 L r y5 A ch2 ch2 0 cd4 L ch y6 L ch y7 A ch2 ch2 0 cd2 Z M x3 ch L x4 ch A ch2 ch2 cd4 -5400000 M x4 ch L x4 ch2 A ch4 ch4 0 cd2 M ch2 y4 L ch2 y3 A ch4 ch4 cd2 cd2 A ch2 ch2 0 cd2 M ch y3 L ch y6","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 12500"]},"cube":{"guides":["a:pin 0 adj 100000","y1:*/ ss a 100000","y4:+- b 0 y1","y2:*/ y4 1 2","y3:+/ y1 b 2","x4:+- r 0 y1","x2:*/ x4 1 2","x3:+/ y1 r 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L x4 y1 L x4 b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x4 y1 L r t L r y4 L x4 b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"LIGHTEN_LESS","filled":true,"h":-1,"path":"M l y1 L y1 t L r t L x4 y1 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l y1 L y1 t L r t L r y4 L x4 b L l b Z M l y1 L x4 y1 L r t M x4 y1 L x4 b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"homePlate":{"guides":["maxAdj:*/ 100000 w ss","a:pin 0 adj maxAdj","dx1:*/ ss a 100000","x1:+- r 0 dx1","ir:+/ x1 r 2","x2:*/ x1 1 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x1 t L r vc L x1 b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 50000"]},"pentagon":{"guides":["swd2:*/ wd2 hf 100000","shd2:*/ hd2 vf 100000","svc:*/ vc  vf 100000","dx1:cos swd2 1080000","dx2:cos swd2 18360000","dy1:sin shd2 1080000","dy2:sin shd2 18360000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","y1:+- svc 0 dy1","y2:+- svc 0 dy2","it:*/ y1 dx2 dx1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 L hc t L x4 y1 L x3 y2 L x2 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["hf:val 105146","vf:val 110557"]},"verticalScroll":{"guides":["a:pin 0 adj 25000","ch:*/ ss a 100000","ch2:*/ ch 1 2","ch4:*/ ch 1 4","x3:+- ch ch2 0","x4:+- ch ch 0","x6:+- r 0 ch","x7:+- r 0 ch2","x5:+- x6 0 ch2","y3:+- b 0 ch","y4:+- b 0 ch2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M ch2 b A ch2 ch2 cd4 -5400000 L ch2 y4 A ch4 ch4 cd4 -10800000 L ch y3 L ch ch2 A ch2 ch2 cd2 cd4 L x7 t A ch2 ch2 3cd4 cd2 L x6 ch L x6 y4 A ch2 ch2 0 cd4 Z M x4 ch2 A ch2 ch2 0 cd4 A ch4 ch4 cd4 cd2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x4 ch2 A ch2 ch2 0 cd4 A ch4 ch4 cd4 cd2 Z M ch y4 A ch2 ch2 0 3cd4 A ch4 ch4 3cd4 cd2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M ch y3 L ch ch2 A ch2 ch2 cd2 cd4 L x7 t A ch2 ch2 3cd4 cd2 L x6 ch L x6 y4 A ch2 ch2 0 cd4 L ch2 b A ch2 ch2 cd4 cd2 Z M x3 t A ch2 ch2 3cd4 cd2 A ch4 ch4 cd4 cd2 L x4 ch2 M x6 ch L x3 ch M ch2 y3 A ch4 ch4 3cd4 cd2 L ch y4 M ch2 b A ch2 ch2 cd4 -5400000 L ch y3","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 12500"]},"roundRect":{"guides":["a:pin 0 adj 50000","x1:*/ ss a 100000","x2:+- r 0 x1","y2:+- b 0 x1","il:*/ x1 29289 100000","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l x1 A x1 x1 cd2 cd4 L x2 t A x1 x1 3cd4 cd4 L r y2 A x1 x1 0 cd4 L x1 b A x1 x1 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 16667"]},"stripedRightArrow":{"guides":["maxAdj2:*/ 84375 w ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","x4:*/ ss 5 32","dx5:*/ ss a2 100000","x5:+- r 0 dx5","dy1:*/ h a1 200000","y1:+- vc 0 dy1","y2:+- vc dy1 0","dx6:*/ dy1 dx5 hd2","x6:+- r 0 dx6"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L ssd32 y1 L ssd32 y2 L l y2 Z M ssd16 y1 L ssd8 y1 L ssd8 y2 L ssd16 y2 Z M x4 y1 L x5 y1 L x5 t L r vc L x5 b L x5 y2 L x4 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"flowChartSort":{"guides":["ir:*/ w 3 4","ib:*/ h 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":2,"path":"M 0 1 L 1 0 L 2 1 L 1 2 Z","stroked":false,"w":2,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":2,"path":"M 0 1 L 2 1","stroked":true,"w":2,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":2,"path":"M 0 1 L 1 0 L 2 1 L 1 2 Z","stroked":true,"w":2,"windingRule":1}],"adjusts":[]},"mathPlus":{"guides":["a1:pin 0 adj1 73490","dx1:*/ w 73490 200000","dy1:*/ h 73490 200000","dx2:*/ ss a1 200000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","y1:+- vc 0 dy1","y2:+- vc 0 dx2","y3:+- vc dx2 0","y4:+- vc dy1 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y2 L x2 y2 L x2 y1 L x3 y1 L x3 y2 L x4 y2 L x4 y3 L x3 y3 L x3 y4 L x2 y4 L x2 y3 L x1 y3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 23520"]},"swooshArrow":{"guides":["a1:pin 1 adj1 75000","maxAdj2:*/ 70000 w ss","a2:pin 0 adj2 maxAdj2","ad1:*/ h a1 100000","ad2:*/ ss a2 100000","xB:+- r 0 ad2","yB:+- t ssd8 0","alfa:*/ cd4 1 14","dx0:tan ssd8 alfa","xC:+- xB 0 dx0","dx1:tan ad1 alfa","yF:+- yB ad1 0","xF:+- xB dx1 0","xE:+- xF dx0 0","yE:+- yF ssd8 0","dy2:+- yE 0 t","dy22:*/ dy2 1 2","dy3:*/ h 1 20","yD:+- t dy22 dy3","dy4:*/ hd6 1 1","yP1:+- hd6 dy4 0","xP1:val wd6","dy5:*/ hd6 1 2","yP2:+- yF dy5 0","xP2:val wd4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b Q xP1 yP1 xB yB L xC t L r yD L xE yE L xF yF Q xP2 yP2 l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 16667"]},"wedgeRectCallout":{"guides":["dxPos:*/ w adj1 100000","dyPos:*/ h adj2 100000","xPos:+- hc dxPos 0","yPos:+- vc dyPos 0","dx:+- xPos 0 hc","dy:+- yPos 0 vc","dq:*/ dxPos h w","ady:abs dyPos","adq:abs dq","dz:+- ady 0 adq","xg1:?: dxPos 7 2","xg2:?: dxPos 10 5","x1:*/ w xg1 12","x2:*/ w xg2 12","yg1:?: dyPos 7 2","yg2:?: dyPos 10 5","y1:*/ h yg1 12","y2:*/ h yg2 12","t1:?: dxPos l xPos","xl:?: dz l t1","t2:?: dyPos x1 xPos","xt:?: dz t2 x1","t3:?: dxPos xPos r","xr:?: dz r t3","t4:?: dyPos xPos x1","xb:?: dz t4 x1","t5:?: dxPos y1 yPos","yl:?: dz y1 t5","t6:?: dyPos t yPos","yt:?: dz t6 t","t7:?: dxPos yPos y1","yr:?: dz y1 t7","t8:?: dyPos yPos b","yb:?: dz t8 b"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x1 t L xt yt L x2 t L r t L r y1 L xr yr L r y2 L r b L x2 b L xb yb L x1 b L l b L l y2 L xl yl L l y1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val -20833","adj2:val 62500"]},"circularArrow":{"guides":["a5:pin 0 adj5 25000","maxAdj1:*/ a5 2 1","a1:pin 0 adj1 maxAdj1","enAng:pin 1 adj3 21599999","stAng:pin 0 adj4 21599999","th:*/ ss a1 100000","thh:*/ ss a5 100000","th2:*/ th 1 2","rw1:+- wd2 th2 thh","rh1:+- hd2 th2 thh","rw2:+- rw1 0 th","rh2:+- rh1 0 th","rw3:+- rw2 th2 0","rh3:+- rh2 th2 0","wtH:sin rw3 enAng","htH:cos rh3 enAng","dxH:cat2 rw3 htH wtH","dyH:sat2 rh3 htH wtH","xH:+- hc dxH 0","yH:+- vc dyH 0","rI:min rw2 rh2","u1:*/ dxH dxH 1","u2:*/ dyH dyH 1","u3:*/ rI rI 1","u4:+- u1 0 u3","u5:+- u2 0 u3","u6:*/ u4 u5 u1","u7:*/ u6 1 u2","u8:+- 1 0 u7","u9:sqrt u8","u10:*/ u4 1 dxH","u11:*/ u10 1 dyH","u12:+/ 1 u9 u11","u13:at2 1 u12","u14:+- u13 21600000 0","u15:?: u13 u13 u14","u16:+- u15 0 enAng","u17:+- u16 21600000 0","u18:?: u16 u16 u17","u19:+- u18 0 cd2","u20:+- u18 0 21600000","u21:?: u19 u20 u18","maxAng:abs u21","aAng:pin 0 adj2 maxAng","ptAng:+- enAng aAng 0","wtA:sin rw3 ptAng","htA:cos rh3 ptAng","dxA:cat2 rw3 htA wtA","dyA:sat2 rh3 htA wtA","xA:+- hc dxA 0","yA:+- vc dyA 0","wtE:sin rw1 stAng","htE:cos rh1 stAng","dxE:cat2 rw1 htE wtE","dyE:sat2 rh1 htE wtE","xE:+- hc dxE 0","yE:+- vc dyE 0","dxG:cos thh ptAng","dyG:sin thh ptAng","xG:+- xH dxG 0","yG:+- yH dyG 0","dxB:cos thh ptAng","dyB:sin thh ptAng","xB:+- xH 0 dxB 0","yB:+- yH 0 dyB 0","sx1:+- xB 0 hc","sy1:+- yB 0 vc","sx2:+- xG 0 hc","sy2:+- yG 0 vc","rO:min rw1 rh1","x1O:*/ sx1 rO rw1","y1O:*/ sy1 rO rh1","x2O:*/ sx2 rO rw1","y2O:*/ sy2 rO rh1","dxO:+- x2O 0 x1O","dyO:+- y2O 0 y1O","dO:mod dxO dyO 0","q1:*/ x1O y2O 1","q2:*/ x2O y1O 1","DO:+- q1 0 q2","q3:*/ rO rO 1","q4:*/ dO dO 1","q5:*/ q3 q4 1","q6:*/ DO DO 1","q7:+- q5 0 q6","q8:max q7 0","sdelO:sqrt q8","ndyO:*/ dyO -1 1","sdyO:?: ndyO -1 1","q9:*/ sdyO dxO 1","q10:*/ q9 sdelO 1","q11:*/ DO dyO 1","dxF1:+/ q11 q10 q4","q12:+- q11 0 q10","dxF2:*/ q12 1 q4","adyO:abs dyO","q13:*/ adyO sdelO 1","q14:*/ DO dxO -1","dyF1:+/ q14 q13 q4","q15:+- q14 0 q13","dyF2:*/ q15 1 q4","q16:+- x2O 0 dxF1","q17:+- x2O 0 dxF2","q18:+- y2O 0 dyF1","q19:+- y2O 0 dyF2","q20:mod q16 q18 0","q21:mod q17 q19 0","q22:+- q21 0 q20","dxF:?: q22 dxF1 dxF2","dyF:?: q22 dyF1 dyF2","sdxF:*/ dxF rw1 rO","sdyF:*/ dyF rh1 rO","xF:+- hc sdxF 0","yF:+- vc sdyF 0","x1I:*/ sx1 rI rw2","y1I:*/ sy1 rI rh2","x2I:*/ sx2 rI rw2","y2I:*/ sy2 rI rh2","dxI:+- x2I 0 x1I","dyI:+- y2I 0 y1I","dI:mod dxI dyI 0","v1:*/ x1I y2I 1","v2:*/ x2I y1I 1","DI:+- v1 0 v2","v3:*/ rI rI 1","v4:*/ dI dI 1","v5:*/ v3 v4 1","v6:*/ DI DI 1","v7:+- v5 0 v6","v8:max v7 0","sdelI:sqrt v8","v9:*/ sdyO dxI 1","v10:*/ v9 sdelI 1","v11:*/ DI dyI 1","dxC1:+/ v11 v10 v4","v12:+- v11 0 v10","dxC2:*/ v12 1 v4","adyI:abs dyI","v13:*/ adyI sdelI 1","v14:*/ DI dxI -1","dyC1:+/ v14 v13 v4","v15:+- v14 0 v13","dyC2:*/ v15 1 v4","v16:+- x1I 0 dxC1","v17:+- x1I 0 dxC2","v18:+- y1I 0 dyC1","v19:+- y1I 0 dyC2","v20:mod v16 v18 0","v21:mod v17 v19 0","v22:+- v21 0 v20","dxC:?: v22 dxC1 dxC2","dyC:?: v22 dyC1 dyC2","sdxC:*/ dxC rw2 rI","sdyC:*/ dyC rh2 rI","xC:+- hc sdxC 0","yC:+- vc sdyC 0","ist0:at2 sdxC sdyC","ist1:+- ist0 21600000 0","istAng:?: ist0 ist0 ist1","isw1:+- stAng 0 istAng","isw2:+- isw1 0 21600000","iswAng:?: isw1 isw2 isw1","p1:+- xF 0 xC","p2:+- yF 0 yC","p3:mod p1 p2 0","p4:*/ p3 1 2","p5:+- p4 0 thh","xGp:?: p5 xF xG","yGp:?: p5 yF yG","xBp:?: p5 xC xB","yBp:?: p5 yC yB","en0:at2 sdxF sdyF","en1:+- en0 21600000 0","en2:?: en0 en0 en1","sw0:+- en2 0 stAng","sw1:+- sw0 21600000 0","swAng:?: sw0 sw0 sw1","wtI:sin rw3 stAng","htI:cos rh3 stAng","dxI:cat2 rw3 htI wtI","dyI:sat2 rh3 htI wtI","xI:+- hc dxI 0","yI:+- vc dyI 0","aI:+- stAng 0 cd4","aA:+- ptAng cd4 0","aB:+- ptAng cd2 0","idx:cos rw1 2700000","idy:sin rh1 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xE yE A rw1 rh1 stAng swAng L xGp yGp L xA yA L xBp yBp L xC yC A rw2 rh2 istAng iswAng Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 12500","adj2:val 1142319","adj3:val 20457681","adj4:val 10800000","adj5:val 12500"]},"moon":{"guides":["a:pin 0 adj 87500","g0:*/ ss a 100000","g0w:*/ g0 w ss","g1:+- ss 0 g0","g2:*/ g0 g0 g1","g3:*/ ss ss g1","g4:*/ g3 2 1","g5:+- g4 0 g2","g6:+- g5 0 g0","g6w:*/ g6 w ss","g7:*/ g5 1 2","g8:+- g7 0 g0","dy1:*/ g8 hd2 ss","g10h:+- vc 0 dy1","g11h:+- vc dy1 0","g12:*/ g0 9598 32768","g12w:*/ g12 w ss","g13:+- ss 0 g12","q1:*/ ss ss 1","q2:*/ g13 g13 1","q3:+- q1 0 q2","q4:sqrt q3","dy4:*/ q4 hd2 ss","g15h:+- vc 0 dy4","g16h:+- vc dy4 0","g17w:+- g6w 0 g0w","g18w:*/ g17w 1 2","dx2p:+- g0w g18w w","dx2:*/ dx2p -1 1","dy2:*/ hd2 -1 1","stAng1:at2 dx2 dy2","enAngp1:at2 dx2 hd2","enAng1:+- enAngp1 0 21600000","swAng1:+- enAng1 0 stAng1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r b A w hd2 cd4 cd2 A g18w dy1 stAng1 swAng1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 50000"]},"leftRightCircularArrow":{"guides":["a5:pin 0 adj5 25000","maxAdj1:*/ a5 2 1","a1:pin 0 adj1 maxAdj1","enAng:pin 1 adj3 21599999","stAng:pin 0 adj4 21599999","th:*/ ss a1 100000","thh:*/ ss a5 100000","th2:*/ th 1 2","rw1:+- wd2 th2 thh","rh1:+- hd2 th2 thh","rw2:+- rw1 0 th","rh2:+- rh1 0 th","rw3:+- rw2 th2 0","rh3:+- rh2 th2 0","wtH:sin rw3 enAng","htH:cos rh3 enAng","dxH:cat2 rw3 htH wtH","dyH:sat2 rh3 htH wtH","xH:+- hc dxH 0","yH:+- vc dyH 0","rI:min rw2 rh2","u1:*/ dxH dxH 1","u2:*/ dyH dyH 1","u3:*/ rI rI 1","u4:+- u1 0 u3","u5:+- u2 0 u3","u6:*/ u4 u5 u1","u7:*/ u6 1 u2","u8:+- 1 0 u7","u9:sqrt u8","u10:*/ u4 1 dxH","u11:*/ u10 1 dyH","u12:+/ 1 u9 u11","u13:at2 1 u12","u14:+- u13 21600000 0","u15:?: u13 u13 u14","u16:+- u15 0 enAng","u17:+- u16 21600000 0","u18:?: u16 u16 u17","u19:+- u18 0 cd2","u20:+- u18 0 21600000","u21:?: u19 u20 u18","maxAng:abs u21","aAng:pin 0 adj2 maxAng","ptAng:+- enAng aAng 0","wtA:sin rw3 ptAng","htA:cos rh3 ptAng","dxA:cat2 rw3 htA wtA","dyA:sat2 rh3 htA wtA","xA:+- hc dxA 0","yA:+- vc dyA 0","dxG:cos thh ptAng","dyG:sin thh ptAng","xG:+- xH dxG 0","yG:+- yH dyG 0","dxB:cos thh ptAng","dyB:sin thh ptAng","xB:+- xH 0 dxB 0","yB:+- yH 0 dyB 0","sx1:+- xB 0 hc","sy1:+- yB 0 vc","sx2:+- xG 0 hc","sy2:+- yG 0 vc","rO:min rw1 rh1","x1O:*/ sx1 rO rw1","y1O:*/ sy1 rO rh1","x2O:*/ sx2 rO rw1","y2O:*/ sy2 rO rh1","dxO:+- x2O 0 x1O","dyO:+- y2O 0 y1O","dO:mod dxO dyO 0","q1:*/ x1O y2O 1","q2:*/ x2O y1O 1","DO:+- q1 0 q2","q3:*/ rO rO 1","q4:*/ dO dO 1","q5:*/ q3 q4 1","q6:*/ DO DO 1","q7:+- q5 0 q6","q8:max q7 0","sdelO:sqrt q8","ndyO:*/ dyO -1 1","sdyO:?: ndyO -1 1","q9:*/ sdyO dxO 1","q10:*/ q9 sdelO 1","q11:*/ DO dyO 1","dxF1:+/ q11 q10 q4","q12:+- q11 0 q10","dxF2:*/ q12 1 q4","adyO:abs dyO","q13:*/ adyO sdelO 1","q14:*/ DO dxO -1","dyF1:+/ q14 q13 q4","q15:+- q14 0 q13","dyF2:*/ q15 1 q4","q16:+- x2O 0 dxF1","q17:+- x2O 0 dxF2","q18:+- y2O 0 dyF1","q19:+- y2O 0 dyF2","q20:mod q16 q18 0","q21:mod q17 q19 0","q22:+- q21 0 q20","dxF:?: q22 dxF1 dxF2","dyF:?: q22 dyF1 dyF2","sdxF:*/ dxF rw1 rO","sdyF:*/ dyF rh1 rO","xF:+- hc sdxF 0","yF:+- vc sdyF 0","x1I:*/ sx1 rI rw2","y1I:*/ sy1 rI rh2","x2I:*/ sx2 rI rw2","y2I:*/ sy2 rI rh2","dxI:+- x2I 0 x1I","dyI:+- y2I 0 y1I","dI:mod dxI dyI 0","v1:*/ x1I y2I 1","v2:*/ x2I y1I 1","DI:+- v1 0 v2","v3:*/ rI rI 1","v4:*/ dI dI 1","v5:*/ v3 v4 1","v6:*/ DI DI 1","v7:+- v5 0 v6","v8:max v7 0","sdelI:sqrt v8","v9:*/ sdyO dxI 1","v10:*/ v9 sdelI 1","v11:*/ DI dyI 1","dxC1:+/ v11 v10 v4","v12:+- v11 0 v10","dxC2:*/ v12 1 v4","adyI:abs dyI","v13:*/ adyI sdelI 1","v14:*/ DI dxI -1","dyC1:+/ v14 v13 v4","v15:+- v14 0 v13","dyC2:*/ v15 1 v4","v16:+- x1I 0 dxC1","v17:+- x1I 0 dxC2","v18:+- y1I 0 dyC1","v19:+- y1I 0 dyC2","v20:mod v16 v18 0","v21:mod v17 v19 0","v22:+- v21 0 v20","dxC:?: v22 dxC1 dxC2","dyC:?: v22 dyC1 dyC2","sdxC:*/ dxC rw2 rI","sdyC:*/ dyC rh2 rI","xC:+- hc sdxC 0","yC:+- vc sdyC 0","wtI:sin rw3 stAng","htI:cos rh3 stAng","dxI:cat2 rw3 htI wtI","dyI:sat2 rh3 htI wtI","xI:+- hc dxI 0","yI:+- vc dyI 0","lptAng:+- stAng 0 aAng","wtL:sin rw3 lptAng","htL:cos rh3 lptAng","dxL:cat2 rw3 htL wtL","dyL:sat2 rh3 htL wtL","xL:+- hc dxL 0","yL:+- vc dyL 0","dxK:cos thh lptAng","dyK:sin thh lptAng","xK:+- xI dxK 0","yK:+- yI dyK 0","dxJ:cos thh lptAng","dyJ:sin thh lptAng","xJ:+- xI 0 dxJ 0","yJ:+- yI 0 dyJ 0","p1:+- xF 0 xC","p2:+- yF 0 yC","p3:mod p1 p2 0","p4:*/ p3 1 2","p5:+- p4 0 thh","xGp:?: p5 xF xG","yGp:?: p5 yF yG","xBp:?: p5 xC xB","yBp:?: p5 yC yB","en0:at2 sdxF sdyF","en1:+- en0 21600000 0","en2:?: en0 en0 en1","od0:+- en2 0 enAng","od1:+- od0 21600000 0","od2:?: od0 od0 od1","st0:+- stAng 0 od2","st1:+- st0 21600000 0","st2:?: st0 st0 st1","sw0:+- en2 0 st2","sw1:+- sw0 21600000 0","swAng:?: sw0 sw0 sw1","ist0:at2 sdxC sdyC","ist1:+- ist0 21600000 0","istAng:?: ist0 ist0 ist1","id0:+- istAng 0 enAng","id1:+- id0 0 21600000","id2:?: id0 id1 id0","ien0:+- stAng 0 id2","ien1:+- ien0 0 21600000","ien2:?: ien1 ien1 ien0","isw1:+- ien2 0 istAng","isw2:+- isw1 0 21600000","iswAng:?: isw1 isw2 isw1","wtE:sin rw1 st2","htE:cos rh1 st2","dxE:cat2 rw1 htE wtE","dyE:sat2 rh1 htE wtE","xE:+- hc dxE 0","yE:+- vc dyE 0","wtD:sin rw2 ien2","htD:cos rh2 ien2","dxD:cat2 rw2 htD wtD","dyD:sat2 rh2 htD wtD","xD:+- hc dxD 0","yD:+- vc dyD 0","xKp:?: p5 xE xK","yKp:?: p5 yE yK","xJp:?: p5 xD xJ","yJp:?: p5 yD yJ","aL:+- lptAng 0 cd4","aA:+- ptAng cd4 0","aB:+- ptAng cd2 0","aJ:+- lptAng cd2 0","idx:cos rw1 2700000","idy:sin rh1 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xL yL L xKp yKp L xE yE A rw1 rh1 st2 swAng L xGp yGp L xA yA L xBp yBp L xC yC A rw2 rh2 istAng iswAng L xJp yJp Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 12500","adj2:val 1142319","adj3:val 20457681","adj4:val 11942319","adj5:val 12500"]},"uturnArrow":{"guides":["a2:pin 0 adj2 25000","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","q2:*/ a1 ss h","q3:+- 100000 0 q2","maxAdj3:*/ q3 h ss","a3:pin 0 adj3 maxAdj3","q1:+- a3 a1 0","minAdj5:*/ q1 ss h","a5:pin minAdj5 adj5 100000","th:*/ ss a1 100000","aw2:*/ ss a2 100000","th2:*/ th 1 2","dh2:+- aw2 0 th2","y5:*/ h a5 100000","ah:*/ ss a3 100000","y4:+- y5 0 ah","x9:+- r 0 dh2","bw:*/ x9 1 2","bs:min bw y4","maxAdj4:*/ bs 100000 ss","a4:pin 0 adj4 maxAdj4","bd:*/ ss a4 100000","bd3:+- bd 0 th","bd2:max bd3 0","x3:+- th bd2 0","x8:+- r 0 aw2","x6:+- x8 0 aw2","x7:+- x6 dh2 0","x4:+- x9 0 bd","x5:+- x7 0 bd2","cx:+/ th x7 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L l bd A bd bd cd2 cd4 L x4 t A bd bd 3cd4 cd4 L x9 y4 L r y4 L x8 y5 L x6 y4 L x7 y4 L x7 x3 A bd2 bd2 0 -5400000 L x3 th A bd2 bd2 3cd4 -5400000 L th b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 43750","adj5:val 75000"]},"heptagon":{"guides":["swd2:*/ wd2 hf 100000","shd2:*/ hd2 vf 100000","svc:*/ vc  vf 100000","dx1:*/ swd2 97493 100000","dx2:*/ swd2 78183 100000","dx3:*/ swd2 43388 100000","dy1:*/ shd2 62349 100000","dy2:*/ shd2 22252 100000","dy3:*/ shd2 90097 100000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc 0 dx3","x4:+- hc dx3 0","x5:+- hc dx2 0","x6:+- hc dx1 0","y1:+- svc 0 dy1","y2:+- svc dy2 0","y3:+- svc dy3 0","ib:+- b 0 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y2 L x2 y1 L hc t L x5 y1 L x6 y2 L x4 y3 L x3 y3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["hf:val 102572","vf:val 105210"]},"star16":{"guides":["a:pin 0 adj 50000","dx1:*/ wd2 92388 100000","dx2:*/ wd2 70711 100000","dx3:*/ wd2 38268 100000","dy1:*/ hd2 92388 100000","dy2:*/ hd2 70711 100000","dy3:*/ hd2 38268 100000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc 0 dx3","x4:+- hc dx3 0","x5:+- hc dx2 0","x6:+- hc dx1 0","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc 0 dy3","y4:+- vc dy3 0","y5:+- vc dy2 0","y6:+- vc dy1 0","iwd2:*/ wd2 a 50000","ihd2:*/ hd2 a 50000","sdx1:*/ iwd2 98079 100000","sdx2:*/ iwd2 83147 100000","sdx3:*/ iwd2 55557 100000","sdx4:*/ iwd2 19509 100000","sdy1:*/ ihd2 98079 100000","sdy2:*/ ihd2 83147 100000","sdy3:*/ ihd2 55557 100000","sdy4:*/ ihd2 19509 100000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc 0 sdx3","sx4:+- hc 0 sdx4","sx5:+- hc sdx4 0","sx6:+- hc sdx3 0","sx7:+- hc sdx2 0","sx8:+- hc sdx1 0","sy1:+- vc 0 sdy1","sy2:+- vc 0 sdy2","sy3:+- vc 0 sdy3","sy4:+- vc 0 sdy4","sy5:+- vc sdy4 0","sy6:+- vc sdy3 0","sy7:+- vc sdy2 0","sy8:+- vc sdy1 0","idx:cos iwd2 2700000","idy:sin ihd2 2700000","il:+- hc 0 idx","it:+- vc 0 idy","ir:+- hc idx 0","ib:+- vc idy 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L sx1 sy4 L x1 y3 L sx2 sy3 L x2 y2 L sx3 sy2 L x3 y1 L sx4 sy1 L hc t L sx5 sy1 L x4 y1 L sx6 sy2 L x5 y2 L sx7 sy3 L x6 y3 L sx8 sy4 L r vc L sx8 sy5 L x6 y4 L sx7 sy6 L x5 y5 L sx6 sy7 L x4 y6 L sx5 sy8 L hc b L sx4 sy8 L x3 y6 L sx3 sy7 L x2 y5 L sx2 sy6 L x1 y4 L sx1 sy5 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 37500"]},"accentCallout1":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 t Z L x1 b","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 112500","adj4:val -38333"]},"curvedRightArrow":{"guides":["maxAdj2:*/ 50000 h ss","a2:pin 0 adj2 maxAdj2","a1:pin 0 adj1 a2","th:*/ ss a1 100000","aw:*/ ss a2 100000","q1:+/ th aw 4","hR:+- hd2 0 q1","q7:*/ hR 2 1","q8:*/ q7 q7 1","q9:*/ th th 1","q10:+- q8 0 q9","q11:sqrt q10","idx:*/ q11 w q7","maxAdj3:*/ 100000 idx ss","a3:pin 0 adj3 maxAdj3","ah:*/ ss a3 100000","y3:+- hR th 0","q2:*/ w w 1","q3:*/ ah ah 1","q4:+- q2 0 q3","q5:sqrt q4","dy:*/ q5 hR w","y5:+- hR dy 0","y7:+- y3 dy 0","q6:+- aw 0 th","dh:*/ q6 1 2","y4:+- y5 0 dh","y8:+- y7 dh 0","aw2:*/ aw 1 2","y6:+- b 0 aw2","x1:+- r 0 ah","swAng:at2 ah dy","stAng:+- cd2 0 swAng","mswAng:+- 0 0 swAng","ix:+- r 0 idx","iy:+/ hR y3 2","q12:*/ th 1 2","dang2:at2 idx q12","swAng2:+- dang2 0 cd4","swAng3:+- cd4 dang2 0","stAng3:+- cd2 0 dang2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l hR A w hR cd2 mswAng L x1 y4 L r y6 L x1 y8 L x1 y7 A w hR stAng swAng Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M r th A w hR 3cd4 swAng2 A w hR stAng3 swAng3 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l hR A w hR cd2 mswAng L x1 y4 L r y6 L x1 y8 L x1 y7 A w hR stAng swAng L l hR A w hR cd2 cd4 L r th A w hR 3cd4 swAng2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 50000","adj3:val 25000"]},"accentCallout2":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 t Z L x1 b","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 112500","adj6:val -46667"]},"actionButtonBackPrevious":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g11 vc L g12 g9 L g12 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g11 vc L g12 g9 L g12 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g11 vc L g12 g9 L g12 g10 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"actionButtonReturn":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0","g13:*/ ss 3 4","g14:*/ g13 7 8","g15:*/ g13 3 4","g16:*/ g13 5 8","g17:*/ g13 3 8","g18:*/ g13 1 4","g19:+- g9 g15 0","g20:+- g9 g16 0","g21:+- g9 g18 0","g22:+- g11 g14 0","g23:+- g11 g15 0","g24:+- g11 g16 0","g25:+- g11 g17 0","g26:+- g11 g18 0","g27:*/ g13 1 8"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g12 g21 L g23 g9 L hc g21 L g24 g21 L g24 g20 A g27 g27 0 cd4 L g25 g19 A g27 g27 cd4 cd4 L g26 g21 L g11 g21 L g11 g20 A g17 g17 cd2 -5400000 L hc g10 A g17 g17 cd4 -5400000 L g22 g21 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g12 g21 L g23 g9 L hc g21 L g24 g21 L g24 g20 A g27 g27 0 cd4 L g25 g19 A g27 g27 cd4 cd4 L g26 g21 L g11 g21 L g11 g20 A g17 g17 cd2 -5400000 L hc g10 A g17 g17 cd4 -5400000 L g22 g21 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g12 g21 L g22 g21 L g22 g20 A g17 g17 0 cd4 L g25 g10 A g17 g17 cd4 cd4 L g11 g21 L g26 g21 L g26 g20 A g27 g27 cd2 -5400000 L hc g19 A g27 g27 cd4 -5400000 L g24 g21 L hc g21 L g23 g9 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"leftUpArrow":{"guides":["a2:pin 0 adj2 50000","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:+- 100000 0 maxAdj1","a3:pin 0 adj3 maxAdj3","x1:*/ ss a3 100000","dx2:*/ ss a2 50000","x2:+- r 0 dx2","y2:+- b 0 dx2","dx4:*/ ss a2 100000","x4:+- r 0 dx4","y4:+- b 0 dx4","dx3:*/ ss a1 200000","x3:+- x4 0 dx3","x5:+- x4 dx3 0","y3:+- y4 0 dx3","y5:+- y4 dx3 0","il:*/ dx3 x1 dx4","cx1:+/ x1 x5 2","cy1:+/ x1 y5 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y4 L x1 y2 L x1 y3 L x3 y3 L x3 x1 L x2 x1 L x4 t L r x1 L x5 x1 L x5 y5 L x1 y5 L x1 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000"]},"star10":{"guides":["a:pin 0 adj 50000","swd2:*/ wd2 hf 100000","dx1:*/ swd2 95106 100000","dx2:*/ swd2 58779 100000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","dy1:*/ hd2 80902 100000","dy2:*/ hd2 30902 100000","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc dy2 0","y4:+- vc dy1 0","iwd2:*/ swd2 a 50000","ihd2:*/ hd2 a 50000","sdx1:*/ iwd2 80902 100000","sdx2:*/ iwd2 30902 100000","sdy1:*/ ihd2 95106 100000","sdy2:*/ ihd2 58779 100000","sx1:+- hc 0 iwd2","sx2:+- hc 0 sdx1","sx3:+- hc 0 sdx2","sx4:+- hc sdx2 0","sx5:+- hc sdx1 0","sx6:+- hc iwd2 0","sy1:+- vc 0 sdy1","sy2:+- vc 0 sdy2","sy3:+- vc sdy2 0","sy4:+- vc sdy1 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y2 L sx2 sy2 L x2 y1 L sx3 sy1 L hc t L sx4 sy1 L x3 y1 L sx5 sy2 L x4 y2 L sx6 vc L x4 y3 L sx5 sy3 L x3 y4 L sx4 sy4 L hc b L sx3 sy4 L x2 y4 L sx2 sy3 L x1 y3 L sx1 vc Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 42533","hf:val 105146"]},"accentCallout3":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000","y4:*/ h adj7 100000","x4:*/ w adj8 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 t Z L x1 b","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3 L x4 y4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 100000","adj6:val -16667","adj7:val 112963","adj8:val -8333"]},"star12":{"guides":["a:pin 0 adj 50000","dx1:cos wd2 1800000","dy1:sin hd2 3600000","x1:+- hc 0 dx1","x3:*/ w 3 4","x4:+- hc dx1 0","y1:+- vc 0 dy1","y3:*/ h 3 4","y4:+- vc dy1 0","iwd2:*/ wd2 a 50000","ihd2:*/ hd2 a 50000","sdx1:cos iwd2 900000","sdx2:cos iwd2 2700000","sdx3:cos iwd2 4500000","sdy1:sin ihd2 4500000","sdy2:sin ihd2 2700000","sdy3:sin ihd2 900000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc 0 sdx3","sx4:+- hc sdx3 0","sx5:+- hc sdx2 0","sx6:+- hc sdx1 0","sy1:+- vc 0 sdy1","sy2:+- vc 0 sdy2","sy3:+- vc 0 sdy3","sy4:+- vc sdy3 0","sy5:+- vc sdy2 0","sy6:+- vc sdy1 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L sx1 sy3 L x1 hd4 L sx2 sy2 L wd4 y1 L sx3 sy1 L hc t L sx4 sy1 L x3 y1 L sx5 sy2 L x4 hd4 L sx6 sy3 L r vc L sx6 sy4 L x4 y3 L sx5 sy5 L x3 y4 L sx4 sy6 L hc b L sx3 sy6 L wd4 y4 L sx2 sy5 L x1 y3 L sx1 sy4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 37500"]},"flowChartManualOperation":{"guides":["x3:*/ w 4 5","x4:*/ w 9 10"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":5,"path":"M 0 0 L 5 0 L 4 5 L 1 5 Z","stroked":true,"w":5,"windingRule":1}],"adjusts":[]},"flowChartManualInput":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":5,"path":"M 0 1 L 5 0 L 5 5 L 0 5 Z","stroked":true,"w":5,"windingRule":1}],"adjusts":[]},"flowChartPunchedCard":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":5,"path":"M 0 1 L 1 0 L 5 0 L 5 5 L 0 5 Z","stroked":true,"w":5,"windingRule":1}],"adjusts":[]},"flowChartMultidocument":{"guides":["y2:*/ h 3675 21600","y8:*/ h 20782 21600","x3:*/ w 9298 21600","x4:*/ w 12286 21600","x5:*/ w 18595 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":21600,"path":"M 0 20782 C 9298 23542 9298 18022 18595 18022 L 18595 3675 L 0 3675 Z M 1532 3675 L 1532 1815 L 20000 1815 L 20000 16252 C 19298 16252 18595 16352 18595 16352 L 18595 3675 Z M 2972 1815 L 2972 0 L 21600 0 L 21600 14392 C 20800 14392 20000 14467 20000 14467 L 20000 1815 Z","stroked":false,"w":21600,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":21600,"path":"M 0 3675 L 18595 3675 L 18595 18022 C 9298 18022 9298 23542 0 20782 Z M 1532 3675 L 1532 1815 L 20000 1815 L 20000 16252 C 19298 16252 18595 16352 18595 16352 M 2972 1815 L 2972 0 L 21600 0 L 21600 14392 C 20800 14392 20000 14467 20000 14467","stroked":true,"w":21600,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":21600,"path":"M 0 20782 C 9298 23542 9298 18022 18595 18022 L 18595 16352 C 18595 16352 19298 16252 20000 16252 L 20000 14467 C 20000 14467 20800 14392 21600 14392 L 21600 0 L 2972 0 L 2972 1815 L 1532 1815 L 1532 3675 L 0 3675 Z","stroked":false,"w":21600,"windingRule":1}],"adjusts":[]},"flowChartInternalStorage":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":1,"path":"M 0 0 L 1 0 L 1 1 L 0 1 Z","stroked":false,"w":1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":8,"path":"M 1 0 L 1 8 M 0 1 L 8 1","stroked":true,"w":8,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":1,"path":"M 0 0 L 1 0 L 1 1 L 0 1 Z","stroked":true,"w":1,"windingRule":1}],"adjusts":[]},"flowChartPunchedTape":{"guides":["y2:*/ h 9 10","ib:*/ h 4 5"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":20,"path":"M 0 2 A 5 2 cd2 -10800000 A 5 2 cd2 cd2 L 20 18 A 5 2 0 -10800000 A 5 2 0 cd2 Z","stroked":true,"w":20,"windingRule":1}],"adjusts":[]},"snip2SameRect":{"guides":["a1:pin 0 adj1 50000","a2:pin 0 adj2 50000","tx1:*/ ss a1 100000","tx2:+- r 0 tx1","bx1:*/ ss a2 100000","bx2:+- r 0 bx1","by1:+- b 0 bx1","d:+- tx1 0 bx1","dx:?: d tx1 bx1","il:*/ dx 1 2","ir:+- r 0 il","it:*/ tx1 1 2","ib:+/ by1 b 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M tx1 t L tx2 t L r tx1 L r by1 L bx2 b L bx1 b L l by1 L l tx1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16667","adj2:val 0"]},"actionButtonBeginning":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0","g13:*/ ss 3 4","g14:*/ g13 1 8","g15:*/ g13 1 4","g16:+- g11 g14 0","g17:+- g11 g15 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g17 vc L g12 g9 L g12 g10 Z M g16 g9 L g11 g9 L g11 g10 L g16 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g17 vc L g12 g9 L g12 g10 Z M g16 g9 L g11 g9 L g11 g10 L g16 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g17 vc L g12 g9 L g12 g10 Z M g16 g9 L g16 g10 L g11 g10 L g11 g9 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"flowChartTerminator":{"guides":["il:*/ w 1018 21600","ir:*/ w 20582 21600","it:*/ h 3163 21600","ib:*/ h 18437 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":21600,"path":"M 3475 0 L 18125 0 A 3475 10800 3cd4 cd2 L 3475 21600 A 3475 10800 cd4 cd2 Z","stroked":true,"w":21600,"windingRule":1}],"adjusts":[]},"notchedRightArrow":{"guides":["maxAdj2:*/ 100000 w ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","dx2:*/ ss a2 100000","x2:+- r 0 dx2","dy1:*/ h a1 200000","y1:+- vc 0 dy1","y2:+- vc dy1 0","x1:*/ dy1 dx2 hd2","x3:+- r 0 x1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L x2 y1 L x2 t L r vc L x2 b L x2 y2 L l y2 L x1 vc Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"actionButtonEnd":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0","g13:*/ ss 3 4","g14:*/ g13 3 4","g15:*/ g13 7 8","g16:+- g11 g14 0","g17:+- g11 g15 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g16 vc L g11 g9 L g11 g10 Z M g17 g9 L g12 g9 L g12 g10 L g17 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g16 vc L g11 g9 L g11 g10 Z M g17 g9 L g12 g9 L g12 g10 L g17 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g16 vc L g11 g10 L g11 g9 Z M g17 g9 L g12 g9 L g12 g10 L g17 g10 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"leftArrowCallout":{"guides":["maxAdj2:*/ 50000 h ss","a2:pin 0 adj2 maxAdj2","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:*/ 100000 w ss","a3:pin 0 adj3 maxAdj3","q2:*/ a3 ss w","maxAdj4:+- 100000 0 q2","a4:pin 0 adj4 maxAdj4","dy1:*/ ss a2 100000","dy2:*/ ss a1 200000","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc dy2 0","y4:+- vc dy1 0","x1:*/ ss a3 100000","dx2:*/ w a4 100000","x2:+- r 0 dx2","x3:+/ x2 r 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x1 y1 L x1 y2 L x2 y2 L x2 t L r t L r b L x2 b L x2 y3 L x1 y3 L x1 y4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 64977"]},"mathEqual":{"guides":["a1:pin 0 adj1 36745","2a1:*/ a1 2 1","mAdj2:+- 100000 0 2a1","a2:pin 0 adj2 mAdj2","dy1:*/ h a1 100000","dy2:*/ h a2 200000","dx1:*/ w 73490 200000","y2:+- vc 0 dy2","y3:+- vc dy2 0","y1:+- y2 0 dy1","y4:+- y3 dy1 0","x1:+- hc 0 dx1","x2:+- hc dx1 0","yC1:+/ y1 y2 2","yC2:+/ y3 y4 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 L x2 y1 L x2 y2 L x1 y2 Z M x1 y3 L x2 y3 L x2 y4 L x1 y4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 23520","adj2:val 11760"]},"sun":{"guides":["a:pin 12500 adj 46875","g0:+- 50000 0 a","g1:*/ g0 30274 32768","g2:*/ g0 12540 32768","g3:+- g1 50000 0","g4:+- g2 50000 0","g5:+- 50000 0 g1","g6:+- 50000 0 g2","g7:*/ g0 23170 32768","g8:+- 50000 g7 0","g9:+- 50000 0 g7","g10:*/ g5 3 4","g11:*/ g6 3 4","g12:+- g10 3662 0","g13:+- g11 3662 0","g14:+- g11 12500 0","g15:+- 100000 0 g10","g16:+- 100000 0 g12","g17:+- 100000 0 g13","g18:+- 100000 0 g14","ox1:*/ w 18436 21600","oy1:*/ h 3163 21600","ox2:*/ w 3163 21600","oy2:*/ h 18436 21600","x8:*/ w g8 100000","x9:*/ w g9 100000","x10:*/ w g10 100000","x12:*/ w g12 100000","x13:*/ w g13 100000","x14:*/ w g14 100000","x15:*/ w g15 100000","x16:*/ w g16 100000","x17:*/ w g17 100000","x18:*/ w g18 100000","x19:*/ w a 100000","wR:*/ w g0 100000","hR:*/ h g0 100000","y8:*/ h g8 100000","y9:*/ h g9 100000","y10:*/ h g10 100000","y12:*/ h g12 100000","y13:*/ h g13 100000","y14:*/ h g14 100000","y15:*/ h g15 100000","y16:*/ h g16 100000","y17:*/ h g17 100000","y18:*/ h g18 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r vc L x15 y18 L x15 y14 Z M ox1 oy1 L x16 y13 L x17 y12 Z M hc t L x18 y10 L x14 y10 Z M ox2 oy1 L x13 y12 L x12 y13 Z M l vc L x10 y14 L x10 y18 Z M ox2 oy2 L x12 y17 L x13 y16 Z M hc b L x14 y15 L x18 y15 Z M ox1 oy2 L x17 y16 L x16 y17 Z M x19 vc A wR hR cd2 21600000 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"triangle":{"guides":["a:pin 0 adj 100000","x1:*/ w a 200000","x2:*/ w a 100000","x3:+- x1 wd2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L x2 t L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 50000"]},"gear9":{"guides":["a1:pin 0 adj1 20000","a2:pin 0 adj2 2679","th:*/ ss a1 100000","lFD:*/ ss a2 100000","th2:*/ th 1 2","l2:*/ lFD 1 2","l3:+- th2 l2 0","rh:+- hd2 0 th","rw:+- wd2 0 th","dr:+- rw 0 rh","maxr:?: dr rh rw","ha:at2 maxr l3","aA1:+- 18600000 0 ha","aD1:+- 18600000 ha 0","ta11:cos rw aA1","ta12:sin rh aA1","bA1:at2 ta11 ta12","cta1:cos rh bA1","sta1:sin rw bA1","ma1:mod cta1 sta1 0","na1:*/ rw rh ma1","dxa1:cos na1 bA1","dya1:sin na1 bA1","xA1:+- hc dxa1 0","yA1:+- vc dya1 0","td11:cos rw aD1","td12:sin rh aD1","bD1:at2 td11 td12","ctd1:cos rh bD1","std1:sin rw bD1","md1:mod ctd1 std1 0","nd1:*/ rw rh md1","dxd1:cos nd1 bD1","dyd1:sin nd1 bD1","xD1:+- hc dxd1 0","yD1:+- vc dyd1 0","xAD1:+- xA1 0 xD1","yAD1:+- yA1 0 yD1","lAD1:mod xAD1 yAD1 0","a1:at2 yAD1 xAD1","dxF1:sin lFD a1","dyF1:cos lFD a1","xF1:+- xD1 dxF1 0","yF1:+- yD1 dyF1 0","xE1:+- xA1 0 dxF1","yE1:+- yA1 0 dyF1","yC1t:sin th a1","xC1t:cos th a1","yC1:+- yF1 yC1t 0","xC1:+- xF1 0 xC1t","yB1:+- yE1 yC1t 0","xB1:+- xE1 0 xC1t","aA2:+- 21000000 0 ha","aD2:+- 21000000 ha 0","ta21:cos rw aA2","ta22:sin rh aA2","bA2:at2 ta21 ta22","cta2:cos rh bA2","sta2:sin rw bA2","ma2:mod cta2 sta2 0","na2:*/ rw rh ma2","dxa2:cos na2 bA2","dya2:sin na2 bA2","xA2:+- hc dxa2 0","yA2:+- vc dya2 0","td21:cos rw aD2","td22:sin rh aD2","bD2:at2 td21 td22","ctd2:cos rh bD2","std2:sin rw bD2","md2:mod ctd2 std2 0","nd2:*/ rw rh md2","dxd2:cos nd2 bD2","dyd2:sin nd2 bD2","xD2:+- hc dxd2 0","yD2:+- vc dyd2 0","xAD2:+- xA2 0 xD2","yAD2:+- yA2 0 yD2","lAD2:mod xAD2 yAD2 0","a2:at2 yAD2 xAD2","dxF2:sin lFD a2","dyF2:cos lFD a2","xF2:+- xD2 dxF2 0","yF2:+- yD2 dyF2 0","xE2:+- xA2 0 dxF2","yE2:+- yA2 0 dyF2","yC2t:sin th a2","xC2t:cos th a2","yC2:+- yF2 yC2t 0","xC2:+- xF2 0 xC2t","yB2:+- yE2 yC2t 0","xB2:+- xE2 0 xC2t","swAng1:+- bA2 0 bD1","aA3:+- 1800000 0 ha","aD3:+- 1800000 ha 0","ta31:cos rw aA3","ta32:sin rh aA3","bA3:at2 ta31 ta32","cta3:cos rh bA3","sta3:sin rw bA3","ma3:mod cta3 sta3 0","na3:*/ rw rh ma3","dxa3:cos na3 bA3","dya3:sin na3 bA3","xA3:+- hc dxa3 0","yA3:+- vc dya3 0","td31:cos rw aD3","td32:sin rh aD3","bD3:at2 td31 td32","ctd3:cos rh bD3","std3:sin rw bD3","md3:mod ctd3 std3 0","nd3:*/ rw rh md3","dxd3:cos nd3 bD3","dyd3:sin nd3 bD3","xD3:+- hc dxd3 0","yD3:+- vc dyd3 0","xAD3:+- xA3 0 xD3","yAD3:+- yA3 0 yD3","lAD3:mod xAD3 yAD3 0","a3:at2 yAD3 xAD3","dxF3:sin lFD a3","dyF3:cos lFD a3","xF3:+- xD3 dxF3 0","yF3:+- yD3 dyF3 0","xE3:+- xA3 0 dxF3","yE3:+- yA3 0 dyF3","yC3t:sin th a3","xC3t:cos th a3","yC3:+- yF3 yC3t 0","xC3:+- xF3 0 xC3t","yB3:+- yE3 yC3t 0","xB3:+- xE3 0 xC3t","swAng2:+- bA3 0 bD2","aA4:+- 4200000 0 ha","aD4:+- 4200000 ha 0","ta41:cos rw aA4","ta42:sin rh aA4","bA4:at2 ta41 ta42","cta4:cos rh bA4","sta4:sin rw bA4","ma4:mod cta4 sta4 0","na4:*/ rw rh ma4","dxa4:cos na4 bA4","dya4:sin na4 bA4","xA4:+- hc dxa4 0","yA4:+- vc dya4 0","td41:cos rw aD4","td42:sin rh aD4","bD4:at2 td41 td42","ctd4:cos rh bD4","std4:sin rw bD4","md4:mod ctd4 std4 0","nd4:*/ rw rh md4","dxd4:cos nd4 bD4","dyd4:sin nd4 bD4","xD4:+- hc dxd4 0","yD4:+- vc dyd4 0","xAD4:+- xA4 0 xD4","yAD4:+- yA4 0 yD4","lAD4:mod xAD4 yAD4 0","a4:at2 yAD4 xAD4","dxF4:sin lFD a4","dyF4:cos lFD a4","xF4:+- xD4 dxF4 0","yF4:+- yD4 dyF4 0","xE4:+- xA4 0 dxF4","yE4:+- yA4 0 dyF4","yC4t:sin th a4","xC4t:cos th a4","yC4:+- yF4 yC4t 0","xC4:+- xF4 0 xC4t","yB4:+- yE4 yC4t 0","xB4:+- xE4 0 xC4t","swAng3:+- bA4 0 bD3","aA5:+- 6600000 0 ha","aD5:+- 6600000 ha 0","ta51:cos rw aA5","ta52:sin rh aA5","bA5:at2 ta51 ta52","td51:cos rw aD5","td52:sin rh aD5","bD5:at2 td51 td52","xD5:+- w 0 xA4","xC5:+- w 0 xB4","xB5:+- w 0 xC4","swAng4:+- bA5 0 bD4","aD6:+- 9000000 ha 0","td61:cos rw aD6","td62:sin rh aD6","bD6:at2 td61 td62","xD6:+- w 0 xA3","xC6:+- w 0 xB3","xB6:+- w 0 xC3","aD7:+- 11400000 ha 0","td71:cos rw aD7","td72:sin rh aD7","bD7:at2 td71 td72","xD7:+- w 0 xA2","xC7:+- w 0 xB2","xB7:+- w 0 xC2","aD8:+- 13800000 ha 0","td81:cos rw aD8","td82:sin rh aD8","bD8:at2 td81 td82","xA8:+- w 0 xD1","xD8:+- w 0 xA1","xC8:+- w 0 xB1","xB8:+- w 0 xC1","aA9:+- 3cd4 0 ha","aD9:+- 3cd4 ha 0","td91:cos rw aD9","td92:sin rh aD9","bD9:at2 td91 td92","ctd9:cos rh bD9","std9:sin rw bD9","md9:mod ctd9 std9 0","nd9:*/ rw rh md9","dxd9:cos nd9 bD9","dyd9:sin nd9 bD9","xD9:+- hc dxd9 0","yD9:+- vc dyd9 0","ta91:cos rw aA9","ta92:sin rh aA9","bA9:at2 ta91 ta92","xA9:+- hc 0 dxd9","xF9:+- xD9 0 lFD","xE9:+- xA9 lFD 0","yC9:+- yD9 0 th","swAng5:+- bA9 0 bD8","xCxn1:+/ xB1 xC1 2","yCxn1:+/ yB1 yC1 2","xCxn2:+/ xB2 xC2 2","yCxn2:+/ yB2 yC2 2","xCxn3:+/ xB3 xC3 2","yCxn3:+/ yB3 yC3 2","xCxn4:+/ xB4 xC4 2","yCxn4:+/ yB4 yC4 2","xCxn5:+/ r 0 xCxn4","xCxn6:+/ r 0 xCxn3","xCxn7:+/ r 0 xCxn2","xCxn8:+/ r 0 xCxn1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xA1 yA1 L xB1 yB1 L xC1 yC1 L xD1 yD1 A rw rh bD1 swAng1 L xB2 yB2 L xC2 yC2 L xD2 yD2 A rw rh bD2 swAng2 L xB3 yB3 L xC3 yC3 L xD3 yD3 A rw rh bD3 swAng3 L xB4 yB4 L xC4 yC4 L xD4 yD4 A rw rh bD4 swAng4 L xB5 yC4 L xC5 yB4 L xD5 yA4 A rw rh bD5 swAng3 L xB6 yC3 L xC6 yB3 L xD6 yA3 A rw rh bD6 swAng2 L xB7 yC2 L xC7 yB2 L xD7 yA2 A rw rh bD7 swAng1 L xB8 yC1 L xC8 yB1 L xD8 yA1 A rw rh bD8 swAng5 L xE9 yC9 L xF9 yC9 L xD9 yD9 A rw rh bD9 swAng5 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 10000","adj2:val 1763"]},"round1Rect":{"guides":["a:pin 0 adj 50000","dx1:*/ ss a 100000","x1:+- r 0 dx1","idx:*/ dx1 29289 100000","ir:+- r 0 idx"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x1 t A dx1 dx1 3cd4 cd4 L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 16667"]},"arc":{"guides":["stAng:pin 0 adj1 21599999","enAng:pin 0 adj2 21599999","sw11:+- enAng 0 stAng","sw12:+- sw11 21600000 0","swAng:?: sw11 sw11 sw12","wt1:sin wd2 stAng","ht1:cos hd2 stAng","dx1:cat2 wd2 ht1 wt1","dy1:sat2 hd2 ht1 wt1","wt2:sin wd2 enAng","ht2:cos hd2 enAng","dx2:cat2 wd2 ht2 wt2","dy2:sat2 hd2 ht2 wt2","x1:+- hc dx1 0","y1:+- vc dy1 0","x2:+- hc dx2 0","y2:+- vc dy2 0","sw0:+- 21600000 0 stAng","da1:+- swAng 0 sw0","g1:max x1 x2","ir:?: da1 r g1","sw1:+- cd4 0 stAng","sw2:+- 27000000 0 stAng","sw3:?: sw1 sw1 sw2","da2:+- swAng 0 sw3","g5:max y1 y2","ib:?: da2 b g5","sw4:+- cd2 0 stAng","sw5:+- 32400000 0 stAng","sw6:?: sw4 sw4 sw5","da3:+- swAng 0 sw6","g9:min x1 x2","il:?: da3 l g9","sw7:+- 3cd4 0 stAng","sw8:+- 37800000 0 stAng","sw9:?: sw7 sw7 sw8","da4:+- swAng 0 sw9","g13:min y1 y2","it:?: da4 t g13","cang1:+- stAng 0 cd4","cang2:+- enAng cd4 0","cang3:+/ cang1 cang2 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 A wd2 hd2 stAng swAng L hc vc Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 A wd2 hd2 stAng swAng","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16200000","adj2:val 0"]},"upDownArrowCallout":{"guides":["maxAdj2:*/ 50000 w ss","a2:pin 0 adj2 maxAdj2","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:*/ 50000 h ss","a3:pin 0 adj3 maxAdj3","q2:*/ a3 ss hd2","maxAdj4:+- 100000 0 q2","a4:pin 0 adj4 maxAdj4","dx1:*/ ss a2 100000","dx2:*/ ss a1 200000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","y1:*/ ss a3 100000","y4:+- b 0 y1","dy2:*/ h a4 200000","y2:+- vc 0 dy2","y3:+- vc dy2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L x2 y2 L x2 y1 L x1 y1 L hc t L x4 y1 L x3 y1 L x3 y2 L r y2 L r y3 L x3 y3 L x3 y4 L x4 y4 L hc b L x1 y4 L x2 y4 L x2 y3 L l y3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 48123"]},"leftBrace":{"guides":["a2:pin 0 adj2 100000","q1:+- 100000 0 a2","q2:min q1 a2","q3:*/ q2 1 2","maxAdj1:*/ q3 h ss","a1:pin 0 adj1 maxAdj1","y1:*/ ss a1 100000","y3:*/ h a2 100000","y4:+- y3 y1 0","dx1:cos wd2 2700000","dy1:sin y1 2700000","il:+- r 0 dx1","it:+- y1 0 dy1","ib:+- b dy1 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r b A wd2 y1 cd4 cd4 L hc y4 A wd2 y1 0 -5400000 A wd2 y1 cd4 -5400000 L hc y1 A wd2 y1 cd2 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M r b A wd2 y1 cd4 cd4 L hc y4 A wd2 y1 0 -5400000 A wd2 y1 cd4 -5400000 L hc y1 A wd2 y1 cd2 cd4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 8333","adj2:val 50000"]},"flowChartMagneticTape":{"guides":["idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0","ang1:at2 w h"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M hc b A wd2 hd2 cd4 cd4 A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 ang1 L r ib L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"rightBracket":{"guides":["maxAdj:*/ 50000 h ss","a:pin 0 adj maxAdj","y1:*/ ss a 100000","y2:+- b 0 y1","dx1:cos w 2700000","dy1:sin y1 2700000","ir:+- l dx1 0","it:+- y1 0 dy1","ib:+- b dy1 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t A w y1 3cd4 cd4 L r y2 A w y1 0 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t A w y1 3cd4 cd4 L r y2 A w y1 0 cd4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 8333"]},"nonIsoscelesTrapezoid":{"guides":["maxAdj:*/ 50000 w ss","a1:pin 0 adj1 maxAdj","a2:pin 0 adj2 maxAdj","x1:*/ ss a1 200000","x2:*/ ss a1 100000","dx3:*/ ss a2 100000","x3:+- r 0 dx3","x4:+/ r x3 2","il:*/ wd3 a1 maxAdj","adjm:max a1 a2","it:*/ hd3 adjm maxAdj","irt:*/ wd3 a2 maxAdj","ir:+- r 0 irt"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L x2 t L x3 t L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000"]},"ellipseRibbon":{"guides":["a1:pin 0 adj1 100000","a2:pin 25000 adj2 75000","q10:+- 100000 0 a1","q11:*/ q10 1 2","q12:+- a1 0 q11","minAdj3:max 0 q12","a3:pin minAdj3 adj3 a1","dx2:*/ w a2 200000","x2:+- hc 0 dx2","x3:+- x2 wd8 0","x4:+- r 0 x3","x5:+- r 0 x2","x6:+- r 0 wd8","dy1:*/ h a3 100000","f1:*/ 4 dy1 w","q1:*/ x3 x3 w","q2:+- x3 0 q1","y1:*/ f1 q2 1","cx1:*/ x3 1 2","cy1:*/ f1 cx1 1","cx2:+- r 0 cx1","q1:*/ h a1 100000","dy3:+- q1 0 dy1","q3:*/ x2 x2 w","q4:+- x2 0 q3","q5:*/ f1 q4 1","y3:+- q5 dy3 0","q6:+- dy1 dy3 y3","q7:+- q6 dy1 0","cy3:+- q7 dy3 0","rh:+- b 0 q1","q8:*/ dy1 14 16","y2:+/ q8 rh 2","y5:+- q5 rh 0","y6:+- y3 rh 0","cx4:*/ x2 1 2","q9:*/ f1 cx4 1","cy4:+- q9 rh 0","cx5:+- r 0 cx4","cy6:+- cy3 rh 0","y7:+- y1 dy3 0","cy7:+- q1 q1 y7","y8:+- b 0 dy1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t Q cx1 cy1 x3 y1 L x2 y3 Q hc cy3 x5 y3 L x4 y1 Q cx2 cy1 r t L x6 y2 L r rh Q cx5 cy4 x5 y5 L x5 y6 Q hc cy6 x2 y6 L x2 y5 Q cx4 cy4 l rh L wd8 y2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x3 y7 L x3 y1 L x2 y3 Q hc cy3 x5 y3 L x4 y1 L x4 y7 Q hc cy7 x3 y7 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t Q cx1 cy1 x3 y1 L x2 y3 Q hc cy3 x5 y3 L x4 y1 Q cx2 cy1 r t L x6 y2 L r rh Q cx5 cy4 x5 y5 L x5 y6 Q hc cy6 x2 y6 L x2 y5 Q cx4 cy4 l rh L wd8 y2 Z M x2 y5 L x2 y3 M x5 y3 L x5 y5 M x3 y1 L x3 y7 M x4 y7 L x4 y1","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 50000","adj3:val 12500"]},"ribbon2":{"guides":["a1:pin 0 adj1 33333","a2:pin 25000 adj2 75000","x10:+- r 0 wd8","dx2:*/ w a2 200000","x2:+- hc 0 dx2","x9:+- hc dx2 0","x3:+- x2 wd32 0","x8:+- x9 0 wd32","x5:+- x2 wd8 0","x6:+- x9 0 wd8","x4:+- x5 0 wd32","x7:+- x6 wd32 0","dy1:*/ h a1 200000","y1:+- b 0 dy1","dy2:*/ h a1 100000","y2:+- b 0 dy2","y4:+- t dy2 0","y3:+/ y4 b 2","hR:*/ h a1 400000","y6:+- b 0 hR","y7:+- y1 0 hR"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L x4 b A wd32 hR cd4 -10800000 L x3 y1 A wd32 hR cd4 cd2 L x8 y2 A wd32 hR 3cd4 cd2 L x7 y1 A wd32 hR 3cd4 -10800000 L r b L x10 y3 L r y4 L x9 y4 L x9 hR A wd32 hR 0 -5400000 L x3 t A wd32 hR 3cd4 -5400000 L x2 y4 L l y4 L wd8 y3 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x5 y6 A wd32 hR 0 -5400000 L x3 y1 A wd32 hR cd4 cd2 L x5 y2 Z M x6 y6 A wd32 hR cd2 cd4 L x8 y1 A wd32 hR cd4 -10800000 L x6 y2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l b L wd8 y3 L l y4 L x2 y4 L x2 hR A wd32 hR cd2 cd4 L x8 t A wd32 hR 3cd4 cd4 L x9 y4 L x9 y4 L r y4 L x10 y3 L r b L x7 b A wd32 hR cd4 cd2 L x8 y1 A wd32 hR cd4 -10800000 L x3 y2 A wd32 hR 3cd4 -10800000 L x4 y1 A wd32 hR 3cd4 cd2 Z M x5 y2 L x5 y6 M x6 y6 L x6 y2 M x2 y7 L x2 y4 M x9 y4 L x9 y7","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16667","adj2:val 50000"]},"gear6":{"guides":["a1:pin 0 adj1 20000","a2:pin 0 adj2 5358","th:*/ ss a1 100000","lFD:*/ ss a2 100000","th2:*/ th 1 2","l2:*/ lFD 1 2","l3:+- th2 l2 0","rh:+- hd2 0 th","rw:+- wd2 0 th","dr:+- rw 0 rh","maxr:?: dr rh rw","ha:at2 maxr l3","aA1:+- 19800000 0 ha","aD1:+- 19800000 ha 0","ta11:cos rw aA1","ta12:sin rh aA1","bA1:at2 ta11 ta12","cta1:cos rh bA1","sta1:sin rw bA1","ma1:mod cta1 sta1 0","na1:*/ rw rh ma1","dxa1:cos na1 bA1","dya1:sin na1 bA1","xA1:+- hc dxa1 0","yA1:+- vc dya1 0","td11:cos rw aD1","td12:sin rh aD1","bD1:at2 td11 td12","ctd1:cos rh bD1","std1:sin rw bD1","md1:mod ctd1 std1 0","nd1:*/ rw rh md1","dxd1:cos nd1 bD1","dyd1:sin nd1 bD1","xD1:+- hc dxd1 0","yD1:+- vc dyd1 0","xAD1:+- xA1 0 xD1","yAD1:+- yA1 0 yD1","lAD1:mod xAD1 yAD1 0","a1:at2 yAD1 xAD1","dxF1:sin lFD a1","dyF1:cos lFD a1","xF1:+- xD1 dxF1 0","yF1:+- yD1 dyF1 0","xE1:+- xA1 0 dxF1","yE1:+- yA1 0 dyF1","yC1t:sin th a1","xC1t:cos th a1","yC1:+- yF1 yC1t 0","xC1:+- xF1 0 xC1t","yB1:+- yE1 yC1t 0","xB1:+- xE1 0 xC1t","aD6:+- 3cd4 ha 0","td61:cos rw aD6","td62:sin rh aD6","bD6:at2 td61 td62","ctd6:cos rh bD6","std6:sin rw bD6","md6:mod ctd6 std6 0","nd6:*/ rw rh md6","dxd6:cos nd6 bD6","dyd6:sin nd6 bD6","xD6:+- hc dxd6 0","yD6:+- vc dyd6 0","xA6:+- hc 0 dxd6","xF6:+- xD6 0 lFD","xE6:+- xA6 lFD 0","yC6:+- yD6 0 th","swAng1:+- bA1 0 bD6","aA2:+- 1800000 0 ha","aD2:+- 1800000 ha 0","ta21:cos rw aA2","ta22:sin rh aA2","bA2:at2 ta21 ta22","yA2:+- h 0 yD1","td21:cos rw aD2","td22:sin rh aD2","bD2:at2 td21 td22","yD2:+- h 0 yA1","yC2:+- h 0 yB1","yB2:+- h 0 yC1","xB2:val xC1","swAng2:+- bA2 0 bD1","aD3:+- cd4 ha 0","td31:cos rw aD3","td32:sin rh aD3","bD3:at2 td31 td32","yD3:+- h 0 yD6","yB3:+- h 0 yC6","aD4:+- 9000000 ha 0","td41:cos rw aD4","td42:sin rh aD4","bD4:at2 td41 td42","xD4:+- w 0 xD1","xC4:+- w 0 xC1","xB4:+- w 0 xB1","aD5:+- 12600000 ha 0","td51:cos rw aD5","td52:sin rh aD5","bD5:at2 td51 td52","xD5:+- w 0 xA1","xC5:+- w 0 xB1","xB5:+- w 0 xC1","xCxn1:+/ xB1 xC1 2","yCxn1:+/ yB1 yC1 2","yCxn2:+- b 0 yCxn1","xCxn4:+/ r 0 xCxn1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xA1 yA1 L xB1 yB1 L xC1 yC1 L xD1 yD1 A rw rh bD1 swAng2 L xC1 yB2 L xB1 yC2 L xA1 yD2 A rw rh bD2 swAng1 L xF6 yB3 L xE6 yB3 L xA6 yD3 A rw rh bD3 swAng1 L xB4 yC2 L xC4 yB2 L xD4 yA2 A rw rh bD4 swAng2 L xB5 yC1 L xC5 yB1 L xD5 yA1 A rw rh bD5 swAng1 L xE6 yC6 L xF6 yC6 L xD6 yD6 A rw rh bD6 swAng1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 15000","adj2:val 3526"]},"leftRightUpArrow":{"guides":["a2:pin 0 adj2 50000","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","q1:+- 100000 0 maxAdj1","maxAdj3:*/ q1 1 2","a3:pin 0 adj3 maxAdj3","x1:*/ ss a3 100000","dx2:*/ ss a2 100000","x2:+- hc 0 dx2","x5:+- hc dx2 0","dx3:*/ ss a1 200000","x3:+- hc 0 dx3","x4:+- hc dx3 0","x6:+- r 0 x1","dy2:*/ ss a2 50000","y2:+- b 0 dy2","y4:+- b 0 dx2","y3:+- y4 0 dx3","y5:+- y4 dx3 0","il:*/ dx3 x1 dx2","ir:+- r 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y4 L x1 y2 L x1 y3 L x3 y3 L x3 x1 L x2 x1 L hc t L x5 x1 L x4 x1 L x4 y3 L x6 y3 L x6 y2 L r y4 L x6 b L x6 y5 L x1 y5 L x1 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000"]},"curvedConnector3":{"guides":["x2:*/ w adj1 100000","x1:+/ l x2 2","x3:+/ r x2 2","y3:*/ h 3 4"],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t C x1 t x2 hd4 x2 vc C x2 y3 x3 b r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000"]},"curvedConnector2":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t C wd2 t r hd2 r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"curvedConnector5":{"guides":["x3:*/ w adj1 100000","x6:*/ w adj3 100000","x1:+/ x3 x6 2","x2:+/ l x3 2","x4:+/ x3 x1 2","x5:+/ x6 x1 2","x7:+/ x6 r 2","y4:*/ h adj2 100000","y1:+/ t y4 2","y2:+/ t y1 2","y3:+/ y1 y4 2","y5:+/ b y4 2","y6:+/ y5 y4 2","y7:+/ y5 b 2"],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t C x2 t x3 y2 x3 y1 C x3 y3 x4 y4 x1 y4 C x5 y4 x6 y6 x6 y5 C x6 y7 x7 b r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000","adj3:val 50000"]},"downArrow":{"guides":["maxAdj2:*/ 100000 h ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","dy1:*/ ss a2 100000","y1:+- b 0 dy1","dx1:*/ w a1 200000","x1:+- hc 0 dx1","x2:+- hc dx1 0","dy2:*/ x1 dy1 wd2","y2:+- y1 dy2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L x1 y1 L x1 t L x2 t L x2 y1 L r y1 L hc b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"curvedConnector4":{"guides":["x2:*/ w adj1 100000","x1:+/ l x2 2","x3:+/ r x2 2","x4:+/ x2 x3 2","x5:+/ x3 r 2","y4:*/ h adj2 100000","y1:+/ t y4 2","y2:+/ t y1 2","y3:+/ y1 y4 2","y5:+/ b y4 2"],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t C x1 t x2 y2 x2 y1 C x2 y3 x4 y4 x3 y4 C x5 y4 r y5 r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"flowChartOnlineStorage":{"guides":["x2:*/ w 5 6"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":6,"path":"M 1 0 L 6 0 A 1 3 3cd4 -10800000 L 1 6 A 1 3 cd4 cd2 Z","stroked":true,"w":6,"windingRule":1}],"adjusts":[]},"actionButtonDocument":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","dx1:*/ ss 9 32","g11:+- hc 0 dx1","g12:+- hc dx1 0","g13:*/ ss 3 16","g14:+- g12 0 g13","g15:+- g9 g13 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g11 g9 L g14 g9 L g12 g15 L g12 g10 L g11 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M g11 g9 L g14 g9 L g14 g15 L g12 g15 L g12 g10 L g11 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g14 g9 L g14 g15 L g12 g15 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g11 g9 L g14 g9 L g12 g15 L g12 g10 L g11 g10 Z M g12 g15 L g14 g15 L g14 g9","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"mathDivide":{"guides":["a1:pin 1000 adj1 36745","ma1:+- 0 0 a1","ma3h:+/ 73490 ma1 4","ma3w:*/ 36745 w h","maxAdj3:min ma3h ma3w","a3:pin 1000 adj3 maxAdj3","m4a3:*/ -4 a3 1","maxAdj2:+- 73490 m4a3 a1","a2:pin 0 adj2 maxAdj2","dy1:*/ h a1 200000","yg:*/ h a2 100000","rad:*/ h a3 100000","dx1:*/ w 73490 200000","y3:+- vc 0 dy1","y4:+- vc dy1 0","a:+- yg rad 0","y2:+- y3 0 a","y1:+- y2 0 rad","y5:+- b 0 y1","x1:+- hc 0 dx1","x3:+- hc dx1 0","x2:+- hc 0 rad"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M hc y1 A rad rad 3cd4 21600000 Z M hc y5 A rad rad cd4 21600000 Z M x1 y3 L x3 y3 L x3 y4 L x1 y4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 23520","adj2:val 5880","adj3:val 11760"]},"decagon":{"guides":["shd2:*/ hd2 vf 100000","dx1:cos wd2 2160000","dx2:cos wd2 4320000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","dy1:sin shd2 4320000","dy2:sin shd2 2160000","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc dy2 0","y4:+- vc dy1 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x1 y2 L x2 y1 L x3 y1 L x4 y2 L r vc L x4 y3 L x3 y4 L x2 y4 L x1 y3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["vf:val 105146"]},"leftArrow":{"guides":["maxAdj2:*/ 100000 w ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","dx2:*/ ss a2 100000","x2:+- l dx2 0","dy1:*/ h a1 200000","y1:+- vc 0 dy1","y2:+- vc dy1 0","dx1:*/ y1 dx2 hd2","x1:+- x2  0 dx1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x2 t L x2 y1 L r y1 L r y2 L x2 y2 L x2 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"flowChartAlternateProcess":{"guides":["x2:+- r 0 ssd6","y2:+- b 0 ssd6","il:*/ ssd6 29289 100000","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l ssd6 A ssd6 ssd6 cd2 cd4 L x2 t A ssd6 ssd6 3cd4 cd4 L r y2 A ssd6 ssd6 0 cd4 L ssd6 b A ssd6 ssd6 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"borderCallout1":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 112500","adj4:val -38333"]},"flowChartConnector":{"guides":["idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"borderCallout2":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 112500","adj6:val -46667"]},"borderCallout3":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000","y4:*/ h adj7 100000","x4:*/ w adj8 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3 L x4 y4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 100000","adj6:val -16667","adj7:val 112963","adj8:val -8333"]},"diagStripe":{"guides":["a:pin 0 adj 100000","x2:*/ w a 100000","x1:*/ x2 1 2","x3:+/ x2 r 2","y2:*/ h a 100000","y1:*/ y2 1 2","y3:+/ y2 b 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L x2 t L r t L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 50000"]},"chevron":{"guides":["maxAdj:*/ 100000 w ss","a:pin 0 adj maxAdj","x1:*/ ss a 100000","x2:+- r 0 x1","x3:*/ x2 1 2","dx:+- x2 0 x1","il:?: dx x1 l","ir:?: dx x2 r"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x2 t L r vc L x2 b L l b L x1 vc Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 50000"]},"cornerTabs":{"guides":["md:mod w h 0","dx:*/ 1 md 20","y1:+- 0 b dx","x1:+- 0 r dx"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L dx t L l dx Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L dx b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 t L r t L r dx Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r y1 L r b L x1 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"mathMultiply":{"guides":["a1:pin 0 adj1 51965","th:*/ ss a1 100000","a:at2 w h","sa:sin 1 a","ca:cos 1 a","ta:tan 1 a","dl:mod w h 0","rw:*/ dl 51965 100000","lM:+- dl 0 rw","xM:*/ ca lM 2","yM:*/ sa lM 2","dxAM:*/ sa th 2","dyAM:*/ ca th 2","xA:+- xM 0 dxAM","yA:+- yM dyAM 0","xB:+- xM dxAM 0","yB:+- yM 0 dyAM","xBC:+- hc 0 xB","yBC:*/ xBC ta 1","yC:+- yBC yB 0","xD:+- r 0 xB","xE:+- r 0 xA","yFE:+- vc 0 yA","xFE:*/ yFE 1 ta","xF:+- xE 0 xFE","xL:+- xA xFE 0","yG:+- b 0 yA","yH:+- b 0 yB","yI:+- b 0 yC","xC2:+- r 0 xM","yC3:+- b 0 yM"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xA yA L xB yB L hc yC L xD yB L xE yA L xF vc L xE yG L xD yH L hc yI L xB yH L xA yG L xL vc Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 23520"]},"plaque":{"guides":["a:pin 0 adj 50000","x1:*/ ss a 100000","x2:+- r 0 x1","y2:+- b 0 x1","il:*/ x1 70711 100000","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l x1 A x1 x1 cd4 -5400000 L x2 t A x1 x1 cd2 -5400000 L r y2 A x1 x1 3cd4 -5400000 L x1 b A x1 x1 0 -5400000 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 16667"]},"trapezoid":{"guides":["maxAdj:*/ 50000 w ss","a:pin 0 adj maxAdj","x1:*/ ss a 200000","x2:*/ ss a 100000","x3:+- r 0 x2","x4:+- r 0 x1","il:*/ wd3 a maxAdj","it:*/ hd3 a maxAdj","ir:+- r 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L x2 t L x3 t L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"accentBorderCallout3":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000","y4:*/ h adj7 100000","x4:*/ w adj8 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 t Z L x1 b","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3 L x4 y4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 100000","adj6:val -16667","adj7:val 112963","adj8:val -8333"]},"accentBorderCallout2":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 t Z L x1 b","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 112500","adj6:val -46667"]},"teardrop":{"guides":["a:pin 0 adj 200000","r2:sqrt 2","tw:*/ wd2 r2 1","th:*/ hd2 r2 1","sw:*/ tw a 100000","sh:*/ th a 100000","dx1:cos sw 2700000","dy1:sin sh 2700000","x1:+- hc dx1 0","y1:+- vc 0 dy1","x2:+/ hc x1 2","y2:+/ vc y1 2","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 Q x2 t x1 y1 Q r y2 r vc A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 100000"]},"accentBorderCallout1":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 t Z L x1 b","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 112500","adj4:val -38333"]},"bentConnector3":{"guides":["x1:*/ w adj1 100000"],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L x1 t L x1 b L r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000"]},"hexagon":{"guides":["maxAdj:*/ 50000 w ss","a:pin 0 adj maxAdj","shd2:*/ hd2 vf 100000","x1:*/ ss a 100000","x2:+- r 0 x1","dy1:sin shd2 3600000","y1:+- vc 0 dy1","y2:+- vc dy1 0","q1:*/ maxAdj -1 2","q2:+- a q1 0","q3:?: q2 4 2","q4:?: q2 3 2","q5:?: q2 q1 0","q6:+/ a q5 q1","q7:*/ q6 q4 -1","q8:+- q3 q7 0","il:*/ w q8 24","it:*/ h q8 24","ir:+- r 0 il","ib:+- b 0 it"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x1 y1 L x2 y1 L r vc L x2 y2 L x1 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000","vf:val 115470"]},"bentConnector2":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"leftBracket":{"guides":["maxAdj:*/ 50000 h ss","a:pin 0 adj maxAdj","y1:*/ ss a 100000","y2:+- b 0 y1","dx1:cos w 2700000","dy1:sin y1 2700000","il:+- r 0 dx1","it:+- y1 0 dy1","ib:+- b dy1 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M r b A w y1 cd4 cd4 L l y1 A w y1 cd2 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M r b A w y1 cd4 cd4 L l y1 A w y1 cd2 cd4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 8333"]},"mathNotEqual":{"guides":["a1:pin 0 adj1 50000","crAng:pin 4200000 adj2 6600000","2a1:*/ a1 2 1","maxAdj3:+- 100000 0 2a1","a3:pin 0 adj3 maxAdj3","dy1:*/ h a1 100000","dy2:*/ h a3 200000","dx1:*/ w 73490 200000","x1:+- hc 0 dx1","x8:+- hc dx1 0","y2:+- vc 0 dy2","y3:+- vc dy2 0","y1:+- y2 0 dy1","y4:+- y3 dy1 0","cadj2:+- crAng 0 cd4","xadj2:tan hd2 cadj2","len:mod xadj2 hd2 0","bhw:*/ len dy1 hd2","bhw2:*/ bhw 1 2","x7:+- hc xadj2 bhw2","dx67:*/ xadj2 y1 hd2","x6:+- x7 0 dx67","dx57:*/ xadj2 y2 hd2","x5:+- x7 0 dx57","dx47:*/ xadj2 y3 hd2","x4:+- x7 0 dx47","dx37:*/ xadj2 y4 hd2","x3:+- x7 0 dx37","dx27:*/ xadj2 2 1","x2:+- x7 0 dx27","rx7:+- x7 bhw 0","rx6:+- x6 bhw 0","rx5:+- x5 bhw 0","rx4:+- x4 bhw 0","rx3:+- x3 bhw 0","rx2:+- x2 bhw 0","dx7:*/ dy1 hd2 len","rxt:+- x7 dx7 0","lxt:+- rx7 0 dx7","rx:?: cadj2 rxt rx7","lx:?: cadj2 x7 lxt","dy3:*/ dy1 xadj2 len","dy4:+- 0 0 dy3","ry:?: cadj2 dy3 t","ly:?: cadj2 t dy4","dlx:+- w 0 rx","drx:+- w 0 lx","dly:+- h 0 ry","dry:+- h 0 ly","xC1:+/ rx lx 2","xC2:+/ drx dlx 2","yC1:+/ ry ly 2","yC2:+/ y1 y2 2","yC3:+/ y3 y4 2","yC4:+/ dry dly 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 L x6 y1 L lx ly L rx ry L rx6 y1 L x8 y1 L x8 y2 L rx5 y2 L rx4 y3 L x8 y3 L x8 y4 L rx3 y4 L drx dry L dlx dly L x3 y4 L x1 y4 L x1 y3 L x4 y3 L x5 y2 L x1 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 23520","adj2:val 6600000","adj3:val 11760"]},"wedgeEllipseCallout":{"guides":["dxPos:*/ w adj1 100000","dyPos:*/ h adj2 100000","xPos:+- hc dxPos 0","yPos:+- vc dyPos 0","sdx:*/ dxPos h 1","sdy:*/ dyPos w 1","pang:at2 sdx sdy","stAng:+- pang 660000 0","enAng:+- pang 0 660000","dx1:cos wd2 stAng","dy1:sin hd2 stAng","x1:+- hc dx1 0","y1:+- vc dy1 0","dx2:cos wd2 enAng","dy2:sin hd2 enAng","x2:+- hc dx2 0","y2:+- vc dy2 0","stAng1:at2 dx1 dy1","enAng1:at2 dx2 dy2","swAng1:+- enAng1 0 stAng1","swAng2:+- swAng1 21600000 0","swAng:?: swAng1 swAng1 swAng2","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xPos yPos L x1 y1 A wd2 hd2 stAng1 swAng Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val -20833","adj2:val 62500"]},"pie":{"guides":["stAng:pin 0 adj1 21599999","enAng:pin 0 adj2 21599999","sw1:+- enAng 0 stAng","sw2:+- sw1 21600000 0","swAng:?: sw1 sw1 sw2","wt1:sin wd2 stAng","ht1:cos hd2 stAng","dx1:cat2 wd2 ht1 wt1","dy1:sat2 hd2 ht1 wt1","x1:+- hc dx1 0","y1:+- vc dy1 0","wt2:sin wd2 enAng","ht2:cos hd2 enAng","dx2:cat2 wd2 ht2 wt2","dy2:sat2 hd2 ht2 wt2","x2:+- hc dx2 0","y2:+- vc dy2 0","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 A wd2 hd2 stAng swAng L hc vc Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 0","adj2:val 16200000"]},"cloud":{"guides":["il:*/ w 2977 21600","it:*/ h 3262 21600","ir:*/ w 17087 21600","ib:*/ h 17337 21600","g27:*/ w 67 21600","g28:*/ h 21577 21600","g29:*/ w 21582 21600","g30:*/ h 1235 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":43200,"path":"M 3900 14370 A 6753 9190 -11429249 7426832 A 5333 7267 -8646143 5396714 A 4365 5945 -8748475 5983381 A 4857 6595 -7859164 7034504 A 5333 7273 -4722533 6541615 A 6775 9220 -2776035 7816140 A 5785 7867 37501 6842000 A 6752 9215 1347096 6910353 A 7720 10543 3974558 4542661 A 4360 5918 -16496525 8804134 A 4345 5945 -14809710 9151131 Z","stroked":true,"w":43200,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":43200,"path":"M 4693 26177 A 4345 5945 5204520 1585770 M 6928 34899 A 4360 5918 4416628 686848 M 16478 39090 A 6752 9215 8257449 844866 M 28827 34751 A 6752 9215 387196 959901 M 34129 22954 A 5785 7867 -4217541 4255042 M 41798 15354 A 5333 7273 1819082 1665090 M 38324 5426 A 4857 6595 -824660 891534 M 29078 3952 A 4857 6595 -8950887 1091722 M 22141 4720 A 4365 5945 -9809656 1061181 M 14000 5192 A 6753 9190 -4002417 739161 M 4127 15789 A 6753 9190 9459261 711490","stroked":true,"w":43200,"windingRule":1}],"adjusts":[]},"curvedLeftArrow":{"guides":["maxAdj2:*/ 50000 h ss","a2:pin 0 adj2 maxAdj2","a1:pin 0 adj1 a2","th:*/ ss a1 100000","aw:*/ ss a2 100000","q1:+/ th aw 4","hR:+- hd2 0 q1","q7:*/ hR 2 1","q8:*/ q7 q7 1","q9:*/ th th 1","q10:+- q8 0 q9","q11:sqrt q10","idx:*/ q11 w q7","maxAdj3:*/ 100000 idx ss","a3:pin 0 adj3 maxAdj3","ah:*/ ss a3 100000","y3:+- hR th 0","q2:*/ w w 1","q3:*/ ah ah 1","q4:+- q2 0 q3","q5:sqrt q4","dy:*/ q5 hR w","y5:+- hR dy 0","y7:+- y3 dy 0","q6:+- aw 0 th","dh:*/ q6 1 2","y4:+- y5 0 dh","y8:+- y7 dh 0","aw2:*/ aw 1 2","y6:+- b 0 aw2","x1:+- l ah 0","swAng:at2 ah dy","mswAng:+- 0 0 swAng","ix:+- l idx 0","iy:+/ hR y3 2","q12:*/ th 1 2","dang2:at2 idx q12","swAng2:+- dang2 0 swAng","swAng3:+- swAng dang2 0","stAng3:+- 0 0 dang2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y6 L x1 y4 L x1 y5 A w hR swAng swAng2 A w hR stAng3 swAng3 L x1 y8 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M r y3 A w hR 0 -5400000 L l t A w hR 3cd4 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M r y3 A w hR 0 -5400000 L l t A w hR 3cd4 cd4 L r y3 A w hR 0 swAng L x1 y8 L l y6 L x1 y4 L x1 y5 A w hR swAng swAng2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 50000","adj3:val 25000"]},"bentConnector5":{"guides":["x1:*/ w adj1 100000","x3:*/ w adj3 100000","x2:+/ x1 x3 2","y2:*/ h adj2 100000","y1:+/ t y2 2","y3:+/ b y2 2"],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L x1 t L x1 y2 L x3 y2 L x3 b L r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000","adj3:val 50000"]},"leftRightArrow":{"guides":["maxAdj2:*/ 50000 w ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","x2:*/ ss a2 100000","x3:+- r 0 x2","dy:*/ h a1 200000","y1:+- vc 0 dy","y2:+- vc dy 0","dx1:*/ y1 x2 hd2","x1:+- x2 0 dx1","x4:+- x3 dx1 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x2 t L x2 y1 L x3 y1 L x3 t L r vc L x3 b L x3 y2 L x2 y2 L x2 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"bentConnector4":{"guides":["x1:*/ w adj1 100000","x2:+/ x1 r 2","y2:*/ h adj2 100000","y1:+/ t y2 2"],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L x1 t L x1 y2 L r y2 L r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"noSmoking":{"guides":["a:pin 0 adj 50000","dr:*/ ss a 100000","iwd2:+- wd2 0 dr","ihd2:+- hd2 0 dr","ang:at2 w h","ct:cos ihd2 ang","st:sin iwd2 ang","m:mod ct st 0","n:*/ iwd2 ihd2 m","drd2:*/ dr 1 2","dang:at2 n drd2","dang2:*/ dang 2 1","swAng:+- -10800000 dang2 0","t3:at2 w h","stAng1:+- t3 0 dang","stAng2:+- stAng1 0 cd2","ct1:cos ihd2 stAng1","st1:sin iwd2 stAng1","m1:mod ct1 st1 0","n1:*/ iwd2 ihd2 m1","dx1:cos n1 stAng1","dy1:sin n1 stAng1","x1:+- hc dx1 0","y1:+- vc dy1 0","x2:+- hc 0 dx1","y2:+- vc 0 dy1","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z M x1 y1 A iwd2 ihd2 stAng1 swAng Z M x2 y2 A iwd2 ihd2 stAng2 swAng Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 18750"]},"flowChartDecision":{"guides":["ir:*/ w 3 4","ib:*/ h 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":2,"path":"M 0 1 L 1 0 L 2 1 L 1 2 Z","stroked":true,"w":2,"windingRule":1}],"adjusts":[]},"donut":{"guides":["a:pin 0 adj 50000","dr:*/ ss a 100000","iwd2:+- wd2 0 dr","ihd2:+- hd2 0 dr","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z M dr vc A iwd2 ihd2 cd2 -5400000 A iwd2 ihd2 cd4 -5400000 A iwd2 ihd2 0 -5400000 A iwd2 ihd2 3cd4 -5400000 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"ellipse":{"guides":["idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"leftRightArrowCallout":{"guides":["maxAdj2:*/ 50000 h ss","a2:pin 0 adj2 maxAdj2","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:*/ 50000 w ss","a3:pin 0 adj3 maxAdj3","q2:*/ a3 ss wd2","maxAdj4:+- 100000 0 q2","a4:pin 0 adj4 maxAdj4","dy1:*/ ss a2 100000","dy2:*/ ss a1 200000","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc dy2 0","y4:+- vc dy1 0","x1:*/ ss a3 100000","x4:+- r 0 x1","dx2:*/ w a4 200000","x2:+- hc 0 dx2","x3:+- hc dx2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x1 y1 L x1 y2 L x2 y2 L x2 t L x3 t L x3 y2 L x4 y2 L x4 y1 L r vc L x4 y4 L x4 y3 L x3 y3 L x3 b L x2 b L x2 y3 L x1 y3 L x1 y4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 48123"]},"flowChartSummingJunction":{"guides":["idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M il it L ir ib M ir it L il ib","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"squareTabs":{"guides":["md:mod w h 0","dx:*/ 1 md 20","y1:+- 0 b dx","x1:+- 0 r dx"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L dx t L dx dx L l dx Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L dx y1 L dx b L l b Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 t L r t L r dx L x1 dx Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 L r y1 L r b L x1 b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"halfFrame":{"guides":["maxAdj2:*/ 100000 w ss","a2:pin 0 adj2 maxAdj2","x1:*/ ss a2 100000","g1:*/ h x1 w","g2:+- h 0 g1","maxAdj1:*/ 100000 g2 ss","a1:pin 0 adj1 maxAdj1","y1:*/ ss a1 100000","dx2:*/ y1 w h","x2:+- r 0 dx2","dy2:*/ x1 h w","y2:+- b 0 dy2","cx1:*/ x1 1 2","cy1:+/ y2 b 2","cx2:+/ x2 r 2","cy2:*/ y1 1 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L x2 y1 L x1 y1 L x1 y2 L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 33333","adj2:val 33333"]},"chartPlus":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":10,"path":"M 5 0 L 5 10 M 0 5 L 10 5","stroked":true,"w":10,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":10,"path":"M 0 0 L 0 10 L 10 10 L 10 0 Z","stroked":false,"w":10,"windingRule":1}],"adjusts":[]},"ribbon":{"guides":["a1:pin 0 adj1 33333","a2:pin 25000 adj2 75000","x10:+- r 0 wd8","dx2:*/ w a2 200000","x2:+- hc 0 dx2","x9:+- hc dx2 0","x3:+- x2 wd32 0","x8:+- x9 0 wd32","x5:+- x2 wd8 0","x6:+- x9 0 wd8","x4:+- x5 0 wd32","x7:+- x6 wd32 0","y1:*/ h a1 200000","y2:*/ h a1 100000","y4:+- b 0 y2","y3:*/ y4 1 2","hR:*/ h a1 400000","y5:+- b 0 hR","y6:+- y2 0 hR"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x4 t A wd32 hR 3cd4 cd2 L x3 y1 A wd32 hR 3cd4 -10800000 L x8 y2 A wd32 hR cd4 -10800000 L x7 y1 A wd32 hR cd4 cd2 L r t L x10 y3 L r y4 L x9 y4 L x9 y5 A wd32 hR 0 cd4 L x3 b A wd32 hR cd4 cd4 L x2 y4 L l y4 L wd8 y3 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x5 hR A wd32 hR 0 cd4 L x3 y1 A wd32 hR 3cd4 -10800000 L x5 y2 Z M x6 hR A wd32 hR cd2 -5400000 L x8 y1 A wd32 hR 3cd4 cd2 L x6 y2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L x4 t A wd32 hR 3cd4 cd2 L x3 y1 A wd32 hR 3cd4 -10800000 L x8 y2 A wd32 hR cd4 -10800000 L x7 y1 A wd32 hR cd4 cd2 L r t L x10 y3 L r y4 L x9 y4 L x9 y5 A wd32 hR 0 cd4 L x3 b A wd32 hR cd4 cd4 L x2 y4 L l y4 L wd8 y3 Z M x5 hR L x5 y2 M x6 y2 L x6 hR M x2 y4 L x2 y6 M x9 y6 L x9 y4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16667","adj2:val 50000"]},"star4":{"guides":["a:pin 0 adj 50000","iwd2:*/ wd2 a 50000","ihd2:*/ hd2 a 50000","sdx:cos iwd2 2700000","sdy:sin ihd2 2700000","sx1:+- hc 0 sdx","sx2:+- hc sdx 0","sy1:+- vc 0 sdy","sy2:+- vc sdy 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L sx1 sy1 L hc t L sx2 sy1 L r vc L sx2 sy2 L hc b L sx1 sy2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 12500"]},"star5":{"guides":["a:pin 0 adj 50000","swd2:*/ wd2 hf 100000","shd2:*/ hd2 vf 100000","svc:*/ vc  vf 100000","dx1:cos swd2 1080000","dx2:cos swd2 18360000","dy1:sin shd2 1080000","dy2:sin shd2 18360000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","y1:+- svc 0 dy1","y2:+- svc 0 dy2","iwd2:*/ swd2 a 50000","ihd2:*/ shd2 a 50000","sdx1:cos iwd2 20520000","sdx2:cos iwd2 3240000","sdy1:sin ihd2 3240000","sdy2:sin ihd2 20520000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc sdx2 0","sx4:+- hc sdx1 0","sy1:+- svc 0 sdy1","sy2:+- svc 0 sdy2","sy3:+- svc ihd2 0","yAdj:+- svc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 L sx2 sy1 L hc t L sx3 sy1 L x4 y1 L sx4 sy2 L x3 y2 L hc sy3 L x2 y2 L sx1 sy2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 19098","hf:val 105146","vf:val 110557"]},"flowChartDelay":{"guides":["idx:cos wd2 2700000","idy:sin hd2 2700000","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L hc t A wd2 hd2 3cd4 cd2 L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"snip2DiagRect":{"guides":["a1:pin 0 adj1 50000","a2:pin 0 adj2 50000","lx1:*/ ss a1 100000","lx2:+- r 0 lx1","ly1:+- b 0 lx1","rx1:*/ ss a2 100000","rx2:+- r 0 rx1","ry1:+- b 0 rx1","d:+- lx1 0 rx1","dx:?: d lx1 rx1","il:*/ dx 1 2","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M lx1 t L rx2 t L r rx1 L r ly1 L lx2 b L rx1 b L l ry1 L l lx1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 0","adj2:val 16667"]},"star8":{"guides":["a:pin 0 adj 50000","dx1:cos wd2 2700000","x1:+- hc 0 dx1","x2:+- hc dx1 0","dy1:sin hd2 2700000","y1:+- vc 0 dy1","y2:+- vc dy1 0","iwd2:*/ wd2 a 50000","ihd2:*/ hd2 a 50000","sdx1:*/ iwd2 92388 100000","sdx2:*/ iwd2 38268 100000","sdy1:*/ ihd2 92388 100000","sdy2:*/ ihd2 38268 100000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc sdx2 0","sx4:+- hc sdx1 0","sy1:+- vc 0 sdy1","sy2:+- vc 0 sdy2","sy3:+- vc sdy2 0","sy4:+- vc sdy1 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L sx1 sy2 L x1 y1 L sx2 sy1 L hc t L sx3 sy1 L x2 y1 L sx4 sy2 L r vc L sx4 sy3 L x2 y2 L sx3 sy4 L hc b L sx2 sy4 L x1 y2 L sx1 sy3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 37500"]},"heart":{"guides":["dx1:*/ w 49 48","dx2:*/ w 10 48","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","y1:+- t 0 hd3","il:*/ w 1 6","ir:*/ w 5 6","ib:*/ h 2 3"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M hc hd4 C x3 y1 x4 hd4 hc b C x1 hd4 x2 y1 hc hd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"plus":{"guides":["a:pin 0 adj 50000","x1:*/ ss a 100000","x2:+- r 0 x1","y2:+- b 0 x1","d:+- w 0 h","il:?: d l x1","ir:?: d r x2","it:?: d x1 t","ib:?: d y2 b"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l x1 L x1 x1 L x1 t L x2 t L x2 x1 L r x1 L r y2 L x2 y2 L x2 b L x1 b L x1 y2 L l y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"star6":{"guides":["a:pin 0 adj 50000","swd2:*/ wd2 hf 100000","dx1:cos swd2 1800000","x1:+- hc 0 dx1","x2:+- hc dx1 0","y2:+- vc hd4 0","iwd2:*/ swd2 a 50000","ihd2:*/ hd2 a 50000","sdx2:*/ iwd2 1 2","sx1:+- hc 0 iwd2","sx2:+- hc 0 sdx2","sx3:+- hc sdx2 0","sx4:+- hc iwd2 0","sdy1:sin ihd2 3600000","sy1:+- vc 0 sdy1","sy2:+- vc sdy1 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 hd4 L sx2 sy1 L hc t L sx3 sy1 L x2 hd4 L sx4 vc L x2 y2 L sx3 sy2 L hc b L sx2 sy2 L x1 y2 L sx1 vc Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 28868","hf:val 115470"]},"smileyFace":{"guides":["a:pin -4653 adj 4653","x1:*/ w 4969 21699","x2:*/ w 6215 21600","x3:*/ w 13135 21600","x4:*/ w 16640 21600","y1:*/ h 7570 21600","y3:*/ h 16515 21600","dy2:*/ h a 100000","y2:+- y3 0 dy2","y4:+- y3 dy2 0","dy3:*/ h a 50000","y5:+- y4 dy3 0","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0","wR:*/ w 1125 21600","hR:*/ h 1125 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 21600000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x2 y1 A wR hR cd2 21600000 M x3 y1 A wR hR cd2 21600000","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y2 Q hc y5 x4 y2","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l vc A wd2 hd2 cd2 21600000 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 4653"]},"star7":{"guides":["a:pin 0 adj 50000","swd2:*/ wd2 hf 100000","shd2:*/ hd2 vf 100000","svc:*/ vc  vf 100000","dx1:*/ swd2 97493 100000","dx2:*/ swd2 78183 100000","dx3:*/ swd2 43388 100000","dy1:*/ shd2 62349 100000","dy2:*/ shd2 22252 100000","dy3:*/ shd2 90097 100000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc 0 dx3","x4:+- hc dx3 0","x5:+- hc dx2 0","x6:+- hc dx1 0","y1:+- svc 0 dy1","y2:+- svc dy2 0","y3:+- svc dy3 0","iwd2:*/ swd2 a 50000","ihd2:*/ shd2 a 50000","sdx1:*/ iwd2 97493 100000","sdx2:*/ iwd2 78183 100000","sdx3:*/ iwd2 43388 100000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc 0 sdx3","sx4:+- hc sdx3 0","sx5:+- hc sdx2 0","sx6:+- hc sdx1 0","sdy1:*/ ihd2 90097 100000","sdy2:*/ ihd2 22252 100000","sdy3:*/ ihd2 62349 100000","sy1:+- svc 0 sdy1","sy2:+- svc 0 sdy2","sy3:+- svc sdy3 0","sy4:+- svc ihd2 0","yAdj:+- svc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y2 L sx1 sy2 L x2 y1 L sx3 sy1 L hc t L sx4 sy1 L x5 y1 L sx6 sy2 L x6 y2 L sx5 sy3 L x4 y3 L hc sy4 L x3 y3 L sx2 sy3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 34601","hf:val 102572","vf:val 105210"]},"snipRoundRect":{"guides":["a1:pin 0 adj1 50000","a2:pin 0 adj2 50000","x1:*/ ss a1 100000","dx2:*/ ss a2 100000","x2:+- r 0 dx2","il:*/ x1 29289 100000","ir:+/ x2 r 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 t L x2 t L r dx2 L r b L l b L l x1 A x1 x1 cd2 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16667","adj2:val 16667"]},"straightConnector1":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"leftCircularArrow":{"guides":["a5:pin 0 adj5 25000","maxAdj1:*/ a5 2 1","a1:pin 0 adj1 maxAdj1","enAng:pin 1 adj3 21599999","stAng:pin 0 adj4 21599999","th:*/ ss a1 100000","thh:*/ ss a5 100000","th2:*/ th 1 2","rw1:+- wd2 th2 thh","rh1:+- hd2 th2 thh","rw2:+- rw1 0 th","rh2:+- rh1 0 th","rw3:+- rw2 th2 0","rh3:+- rh2 th2 0","wtH:sin rw3 enAng","htH:cos rh3 enAng","dxH:cat2 rw3 htH wtH","dyH:sat2 rh3 htH wtH","xH:+- hc dxH 0","yH:+- vc dyH 0","rI:min rw2 rh2","u1:*/ dxH dxH 1","u2:*/ dyH dyH 1","u3:*/ rI rI 1","u4:+- u1 0 u3","u5:+- u2 0 u3","u6:*/ u4 u5 u1","u7:*/ u6 1 u2","u8:+- 1 0 u7","u9:sqrt u8","u10:*/ u4 1 dxH","u11:*/ u10 1 dyH","u12:+/ 1 u9 u11","u13:at2 1 u12","u14:+- u13 21600000 0","u15:?: u13 u13 u14","u16:+- u15 0 enAng","u17:+- u16 21600000 0","u18:?: u16 u16 u17","u19:+- u18 0 cd2","u20:+- u18 0 21600000","u21:?: u19 u20 u18","u22:abs u21","minAng:*/ u22 -1 1","u23:abs adj2","a2:*/ u23 -1 1","aAng:pin minAng a2 0","ptAng:+- enAng aAng 0","wtA:sin rw3 ptAng","htA:cos rh3 ptAng","dxA:cat2 rw3 htA wtA","dyA:sat2 rh3 htA wtA","xA:+- hc dxA 0","yA:+- vc dyA 0","wtE:sin rw1 stAng","htE:cos rh1 stAng","dxE:cat2 rw1 htE wtE","dyE:sat2 rh1 htE wtE","xE:+- hc dxE 0","yE:+- vc dyE 0","wtD:sin rw2 stAng","htD:cos rh2 stAng","dxD:cat2 rw2 htD wtD","dyD:sat2 rh2 htD wtD","xD:+- hc dxD 0","yD:+- vc dyD 0","dxG:cos thh ptAng","dyG:sin thh ptAng","xG:+- xH dxG 0","yG:+- yH dyG 0","dxB:cos thh ptAng","dyB:sin thh ptAng","xB:+- xH 0 dxB 0","yB:+- yH 0 dyB 0","sx1:+- xB 0 hc","sy1:+- yB 0 vc","sx2:+- xG 0 hc","sy2:+- yG 0 vc","rO:min rw1 rh1","x1O:*/ sx1 rO rw1","y1O:*/ sy1 rO rh1","x2O:*/ sx2 rO rw1","y2O:*/ sy2 rO rh1","dxO:+- x2O 0 x1O","dyO:+- y2O 0 y1O","dO:mod dxO dyO 0","q1:*/ x1O y2O 1","q2:*/ x2O y1O 1","DO:+- q1 0 q2","q3:*/ rO rO 1","q4:*/ dO dO 1","q5:*/ q3 q4 1","q6:*/ DO DO 1","q7:+- q5 0 q6","q8:max q7 0","sdelO:sqrt q8","ndyO:*/ dyO -1 1","sdyO:?: ndyO -1 1","q9:*/ sdyO dxO 1","q10:*/ q9 sdelO 1","q11:*/ DO dyO 1","dxF1:+/ q11 q10 q4","q12:+- q11 0 q10","dxF2:*/ q12 1 q4","adyO:abs dyO","q13:*/ adyO sdelO 1","q14:*/ DO dxO -1","dyF1:+/ q14 q13 q4","q15:+- q14 0 q13","dyF2:*/ q15 1 q4","q16:+- x2O 0 dxF1","q17:+- x2O 0 dxF2","q18:+- y2O 0 dyF1","q19:+- y2O 0 dyF2","q20:mod q16 q18 0","q21:mod q17 q19 0","q22:+- q21 0 q20","dxF:?: q22 dxF1 dxF2","dyF:?: q22 dyF1 dyF2","sdxF:*/ dxF rw1 rO","sdyF:*/ dyF rh1 rO","xF:+- hc sdxF 0","yF:+- vc sdyF 0","x1I:*/ sx1 rI rw2","y1I:*/ sy1 rI rh2","x2I:*/ sx2 rI rw2","y2I:*/ sy2 rI rh2","dxI:+- x2I 0 x1I","dyI:+- y2I 0 y1I","dI:mod dxI dyI 0","v1:*/ x1I y2I 1","v2:*/ x2I y1I 1","DI:+- v1 0 v2","v3:*/ rI rI 1","v4:*/ dI dI 1","v5:*/ v3 v4 1","v6:*/ DI DI 1","v7:+- v5 0 v6","v8:max v7 0","sdelI:sqrt v8","v9:*/ sdyO dxI 1","v10:*/ v9 sdelI 1","v11:*/ DI dyI 1","dxC1:+/ v11 v10 v4","v12:+- v11 0 v10","dxC2:*/ v12 1 v4","adyI:abs dyI","v13:*/ adyI sdelI 1","v14:*/ DI dxI -1","dyC1:+/ v14 v13 v4","v15:+- v14 0 v13","dyC2:*/ v15 1 v4","v16:+- x1I 0 dxC1","v17:+- x1I 0 dxC2","v18:+- y1I 0 dyC1","v19:+- y1I 0 dyC2","v20:mod v16 v18 0","v21:mod v17 v19 0","v22:+- v21 0 v20","dxC:?: v22 dxC1 dxC2","dyC:?: v22 dyC1 dyC2","sdxC:*/ dxC rw2 rI","sdyC:*/ dyC rh2 rI","xC:+- hc sdxC 0","yC:+- vc sdyC 0","ist0:at2 sdxC sdyC","ist1:+- ist0 21600000 0","istAng0:?: ist0 ist0 ist1","isw1:+- stAng 0 istAng0","isw2:+- isw1 21600000 0","iswAng0:?: isw1 isw1 isw2","istAng:+- istAng0 iswAng0 0","iswAng:+- 0 0 iswAng0","p1:+- xF 0 xC","p2:+- yF 0 yC","p3:mod p1 p2 0","p4:*/ p3 1 2","p5:+- p4 0 thh","xGp:?: p5 xF xG","yGp:?: p5 yF yG","xBp:?: p5 xC xB","yBp:?: p5 yC yB","en0:at2 sdxF sdyF","en1:+- en0 21600000 0","en2:?: en0 en0 en1","sw0:+- en2 0 stAng","sw1:+- sw0 0 21600000","swAng:?: sw0 sw1 sw0","stAng0:+- stAng swAng 0","swAng0:+- 0 0 swAng","wtI:sin rw3 stAng","htI:cos rh3 stAng","dxI:cat2 rw3 htI wtI","dyI:sat2 rh3 htI wtI","xI:+- hc dxI 0","yI:+- vc dyI 0","aI:+- stAng cd4 0","aA:+- ptAng 0 cd4","aB:+- ptAng cd2 0","idx:cos rw1 2700000","idy:sin rh1 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M xE yE L xD yD A rw2 rh2 istAng iswAng L xBp yBp L xA yA L xGp yGp L xF yF A rw1 rh1 stAng0 swAng0 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 12500","adj2:val -1142319","adj3:val 1142319","adj4:val 10800000","adj5:val 12500"]},"upArrowCallout":{"guides":["maxAdj2:*/ 50000 w ss","a2:pin 0 adj2 maxAdj2","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:*/ 100000 h ss","a3:pin 0 adj3 maxAdj3","q2:*/ a3 ss h","maxAdj4:+- 100000 0 q2","a4:pin 0 adj4 maxAdj4","dx1:*/ ss a2 100000","dx2:*/ ss a1 200000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","y1:*/ ss a3 100000","dy2:*/ h a4 100000","y2:+- b 0 dy2","y3:+/ y2 b 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L x2 y2 L x2 y1 L x1 y1 L hc t L x4 y1 L x3 y1 L x3 y2 L r y2 L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 64977"]},"actionButtonHelp":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g11:+- hc 0 dx2","g13:*/ ss 3 4","g14:*/ g13 1 7","g15:*/ g13 3 14","g16:*/ g13 2 7","g19:*/ g13 3 7","g20:*/ g13 4 7","g21:*/ g13 17 28","g23:*/ g13 21 28","g24:*/ g13 11 14","g27:+- g9 g16 0","g29:+- g9 g21 0","g30:+- g9 g23 0","g31:+- g9 g24 0","g33:+- g11 g15 0","g36:+- g11 g19 0","g37:+- g11 g20 0","g41:*/ g13 1 14","g42:*/ g13 3 28"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g33 g27 A g16 g16 cd2 cd2 A g14 g15 0 cd4 A g41 g42 3cd4 -5400000 L g37 g30 L g36 g30 L g36 g29 A g14 g15 cd2 cd4 A g41 g42 cd4 -5400000 A g14 g14 0 -10800000 Z M hc g31 A g42 g42 3cd4 21600000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g33 g27 A g16 g16 cd2 cd2 A g14 g15 0 cd4 A g41 g42 3cd4 -5400000 L g37 g30 L g36 g30 L g36 g29 A g14 g15 cd2 cd4 A g41 g42 cd4 -5400000 A g14 g14 0 -10800000 Z M hc g31 A g42 g42 3cd4 21600000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g33 g27 A g16 g16 cd2 cd2 A g14 g15 0 cd4 A g41 g42 3cd4 -5400000 L g37 g30 L g36 g30 L g36 g29 A g14 g15 cd2 cd4 A g41 g42 cd4 -5400000 A g14 g14 0 -10800000 Z M hc g31 A g42 g42 3cd4 21600000 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"flowChartProcess":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":1,"path":"M 0 0 L 1 0 L 1 1 L 0 1 Z","stroked":true,"w":1,"windingRule":1}],"adjusts":[]},"irregularSeal2":{"guides":["x2:*/ w 9722 21600","x5:*/ w 5372 21600","x16:*/ w 11612 21600","x19:*/ w 14640 21600","y2:*/ h 1887 21600","y3:*/ h 6382 21600","y8:*/ h 12877 21600","y14:*/ h 19712 21600","y16:*/ h 18842 21600","y17:*/ h 15935 21600","y24:*/ h 6645 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":21600,"path":"M 11462 4342 L 14790 0 L 14525 5777 L 18007 3172 L 16380 6532 L 21600 6645 L 16985 9402 L 18270 11290 L 16380 12310 L 18877 15632 L 14640 14350 L 14942 17370 L 12180 15935 L 11612 18842 L 9872 17370 L 8700 19712 L 7527 18125 L 4917 21600 L 4805 18240 L 1285 17825 L 3330 15370 L 0 12877 L 3935 11592 L 1172 8270 L 5372 7817 L 4502 3625 L 8550 6382 L 9722 1887 Z","stroked":true,"w":21600,"windingRule":1}],"adjusts":[]},"rtTriangle":{"guides":["it:*/ h 7 12","ir:*/ w 7 12","ib:*/ h 11 12"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L l t L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"frame":{"guides":["a1:pin 0 adj1 50000","x1:*/ ss a1 100000","x4:+- r 0 x1","y4:+- b 0 x1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M x1 x1 L x1 y4 L x4 y4 L x4 x1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 12500"]},"irregularSeal1":{"guides":["x5:*/ w 4627 21600","x12:*/ w 8485 21600","x21:*/ w 16702 21600","x24:*/ w 14522 21600","y3:*/ h 6320 21600","y6:*/ h 8615 21600","y9:*/ h 13937 21600","y18:*/ h 13290 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":21600,"path":"M 10800 5800 L 14522 0 L 14155 5325 L 18380 4457 L 16702 7315 L 21097 8137 L 17607 10475 L 21600 13290 L 16837 12942 L 18145 18095 L 14020 14457 L 13247 19737 L 10532 14935 L 8485 21600 L 7715 15627 L 4762 17617 L 5667 13937 L 135 14587 L 3722 11775 L 0 8615 L 4627 7617 L 370 2295 L 7312 6320 L 8352 2295 Z","stroked":true,"w":21600,"windingRule":1}],"adjusts":[]},"callout2":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 112500","adj6:val -46667"]},"callout3":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000","y3:*/ h adj5 100000","x3:*/ w adj6 100000","y4:*/ h adj7 100000","x4:*/ w adj8 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2 L x3 y3 L x4 y4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 18750","adj4:val -16667","adj5:val 100000","adj6:val -16667","adj7:val 112963","adj8:val -8333"]},"foldedCorner":{"guides":["a:pin 0 adj 50000","dy2:*/ ss a 100000","dy1:*/ dy2 1 5","x1:+- r 0 dy2","x2:+- x1 dy1 0","y2:+- b 0 dy2","y1:+- y2 dy1 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r y2 L x1 b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x1 b L x2 y1 L r y2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 b L x2 y1 L r y2 L x1 b L l b L l t L r t L r y2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 16667"]},"callout1":{"guides":["y1:*/ h adj1 100000","x1:*/ w adj2 100000","y2:*/ h adj3 100000","x2:*/ w adj4 100000"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 y1 L x2 y2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 18750","adj2:val -8333","adj3:val 112500","adj4:val -38333"]},"curvedUpArrow":{"guides":["maxAdj2:*/ 50000 w ss","a2:pin 0 adj2 maxAdj2","a1:pin 0 adj1 100000","th:*/ ss a1 100000","aw:*/ ss a2 100000","q1:+/ th aw 4","wR:+- wd2 0 q1","q7:*/ wR 2 1","q8:*/ q7 q7 1","q9:*/ th th 1","q10:+- q8 0 q9","q11:sqrt q10","idy:*/ q11 h q7","maxAdj3:*/ 100000 idy ss","a3:pin 0 adj3 maxAdj3","ah:*/ ss adj3 100000","x3:+- wR th 0","q2:*/ h h 1","q3:*/ ah ah 1","q4:+- q2 0 q3","q5:sqrt q4","dx:*/ q5 wR h","x5:+- wR dx 0","x7:+- x3 dx 0","q6:+- aw 0 th","dh:*/ q6 1 2","x4:+- x5 0 dh","x8:+- x7 dh 0","aw2:*/ aw 1 2","x6:+- r 0 aw2","y1:+- t ah 0","swAng:at2 ah dx","mswAng:+- 0 0 swAng","iy:+- t idy 0","ix:+/ wR x3 2","q12:*/ th 1 2","dang2:at2 idy q12","swAng2:+- dang2 0 swAng","mswAng2:+- 0 0 swAng2","stAng3:+- cd4 0 swAng","swAng3:+- swAng dang2 0","stAng2:+- cd4 0 dang2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x6 t L x8 y1 L x7 y1 A wR h stAng3 swAng3 A wR h stAng2 swAng2 L x4 y1 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M wR b A wR h cd4 cd4 L th t A wR h cd2 -5400000 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M ix iy A wR h stAng2 swAng2 L x4 y1 L x6 t L x8 y1 L x7 y1 A wR h stAng3 swAng L wR b A wR h cd4 cd4 L th t A wR h cd2 -5400000","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 50000","adj3:val 25000"]},"line":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r b","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"downArrowCallout":{"guides":["maxAdj2:*/ 50000 w ss","a2:pin 0 adj2 maxAdj2","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","maxAdj3:*/ 100000 h ss","a3:pin 0 adj3 maxAdj3","q2:*/ a3 ss h","maxAdj4:+- 100000 0 q2","a4:pin 0 adj4 maxAdj4","dx1:*/ ss a2 100000","dx2:*/ ss a1 200000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc dx2 0","x4:+- hc dx1 0","dy3:*/ ss a3 100000","y3:+- b 0 dy3","y2:*/ h a4 100000","y1:*/ y2 1 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r y2 L x3 y2 L x3 y3 L x4 y3 L hc b L x1 y3 L x2 y3 L x2 y2 L l y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 64977"]},"rightBrace":{"guides":["a2:pin 0 adj2 100000","q1:+- 100000 0 a2","q2:min q1 a2","q3:*/ q2 1 2","maxAdj1:*/ q3 h ss","a1:pin 0 adj1 maxAdj1","y1:*/ ss a1 100000","y3:*/ h a2 100000","y2:+- y3 0 y1","y4:+- b 0 y1","dx1:cos wd2 2700000","dy1:sin y1 2700000","ir:+- l dx1 0","it:+- y1 0 dy1","ib:+- b dy1 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t A wd2 y1 3cd4 cd4 L hc y2 A wd2 y1 cd2 -5400000 A wd2 y1 3cd4 -5400000 L hc y4 A wd2 y1 0 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t A wd2 y1 3cd4 cd4 L hc y2 A wd2 y1 cd2 -5400000 A wd2 y1 3cd4 -5400000 L hc y4 A wd2 y1 0 cd4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 8333","adj2:val 50000"]},"actionButtonSound":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0","g13:*/ ss 3 4","g14:*/ g13 1 8","g15:*/ g13 5 16","g16:*/ g13 5 8","g17:*/ g13 11 16","g18:*/ g13 3 4","g19:*/ g13 7 8","g20:+- g9 g14 0","g21:+- g9 g15 0","g22:+- g9 g17 0","g23:+- g9 g19 0","g24:+- g11 g15 0","g25:+- g11 g16 0","g26:+- g11 g18 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g11 g21 L g11 g22 L g24 g22 L g25 g10 L g25 g9 L g24 g21 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g11 g21 L g11 g22 L g24 g22 L g25 g10 L g25 g9 L g24 g21 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g11 g21 L g24 g21 L g25 g9 L g25 g10 L g24 g22 L g11 g22 Z M g26 g21 L g12 g20 M g26 vc L g12 vc M g26 g22 L g12 g23","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"quadArrow":{"guides":["a2:pin 0 adj2 50000","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","q1:+- 100000 0 maxAdj1","maxAdj3:*/ q1 1 2","a3:pin 0 adj3 maxAdj3","x1:*/ ss a3 100000","dx2:*/ ss a2 100000","x2:+- hc 0 dx2","x5:+- hc dx2 0","dx3:*/ ss a1 200000","x3:+- hc 0 dx3","x4:+- hc dx3 0","x6:+- r 0 x1","y2:+- vc 0 dx2","y5:+- vc dx2 0","y3:+- vc 0 dx3","y4:+- vc dx3 0","y6:+- b 0 x1","il:*/ dx3 x1 dx2","ir:+- r 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L x1 y2 L x1 y3 L x3 y3 L x3 x1 L x2 x1 L hc t L x5 x1 L x4 x1 L x4 y3 L x6 y3 L x6 y2 L r vc L x6 y5 L x6 y4 L x4 y4 L x4 y6 L x5 y6 L hc b L x2 y6 L x3 y6 L x3 y4 L x1 y4 L x1 y5 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 22500","adj2:val 22500","adj3:val 22500"]},"pieWedge":{"guides":["g1:cos w 13500000","g2:sin h 13500000","x1:+- r g1 0","y1:+- b g2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b A w h cd2 cd4 L r b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"wave":{"guides":["a1:pin 0 adj1 20000","a2:pin -10000 adj2 10000","y1:*/ h a1 100000","dy2:*/ y1 10 3","y2:+- y1 0 dy2","y3:+- y1 dy2 0","y4:+- b 0 y1","y5:+- y4 0 dy2","y6:+- y4 dy2 0","dx1:*/ w a2 100000","of2:*/ w a2 50000","x1:abs dx1","dx2:?: of2 0 of2","x2:+- l 0 dx2","dx5:?: of2 of2 0","x5:+- r 0 dx5","dx3:+/ dx2 x5 3","x3:+- x2 dx3 0","x4:+/ x3 x5 2","x6:+- l dx5 0","x10:+- r dx2 0","x7:+- x6 dx3 0","x8:+/ x7 x10 2","x9:+- r 0 x1","xAdj:+- hc dx1 0","xAdj2:+- hc 0 dx1","il:max x2 x6","ir:min x5 x10","it:*/ h a1 50000","ib:+- b 0 it"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x2 y1 C x3 y2 x4 y3 x5 y1 L x10 y4 C x8 y6 x7 y5 x6 y4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 12500","adj2:val 0"]},"lightningBolt":{"guides":["x1:*/ w 5022 21600","x3:*/ w 8472 21600","x4:*/ w 8757 21600","x5:*/ w 10012 21600","x8:*/ w 12860 21600","x9:*/ w 13917 21600","x11:*/ w 16577 21600","y1:*/ h 3890 21600","y2:*/ h 6080 21600","y4:*/ h 7437 21600","y6:*/ h 9705 21600","y7:*/ h 12007 21600","y10:*/ h 14277 21600","y11:*/ h 14915 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":21600,"path":"M 8472 0 L 12860 6080 L 11050 6797 L 16577 12007 L 14767 12877 L 21600 21600 L 10012 14915 L 12222 13987 L 5022 9705 L 7602 8382 L 0 3890 Z","stroked":true,"w":21600,"windingRule":1}],"adjusts":[]},"can":{"guides":["maxAdj:*/ 50000 h ss","a:pin 0 adj maxAdj","y1:*/ ss a 200000","y2:+- y1 y1 0","y3:+- b 0 y1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 A wd2 y1 cd2 -10800000 L r y3 A wd2 y1 0 cd2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"LIGHTEN","filled":true,"h":-1,"path":"M l y1 A wd2 y1 cd2 cd2 A wd2 y1 0 cd2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M r y1 A wd2 y1 0 cd2 A wd2 y1 cd2 cd2 L r y3 A wd2 y1 0 cd2 L l y1","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 25000"]},"rect":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"actionButtonMovie":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0","g13:*/ ss 3 4","g14:*/ g13 1455 21600","g15:*/ g13 1905 21600","g16:*/ g13 2325 21600","g17:*/ g13 16155 21600","g18:*/ g13 17010 21600","g19:*/ g13 19335 21600","g20:*/ g13 19725 21600","g21:*/ g13 20595 21600","g22:*/ g13 5280 21600","g23:*/ g13 5730 21600","g24:*/ g13 6630 21600","g25:*/ g13 7492 21600","g26:*/ g13 9067 21600","g27:*/ g13 9555 21600","g28:*/ g13 13342 21600","g29:*/ g13 14580 21600","g30:*/ g13 15592 21600","g31:+- g11 g14 0","g32:+- g11 g15 0","g33:+- g11 g16 0","g34:+- g11 g17 0","g35:+- g11 g18 0","g36:+- g11 g19 0","g37:+- g11 g20 0","g38:+- g11 g21 0","g39:+- g9 g22 0","g40:+- g9 g23 0","g41:+- g9 g24 0","g42:+- g9 g25 0","g43:+- g9 g26 0","g44:+- g9 g27 0","g45:+- g9 g28 0","g46:+- g9 g29 0","g47:+- g9 g30 0","g48:+- g9 g31 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M g11 g39 L g11 g44 L g31 g44 L g32 g43 L g33 g43 L g33 g47 L g35 g47 L g35 g45 L g36 g45 L g38 g46 L g12 g46 L g12 g41 L g38 g41 L g37 g42 L g35 g42 L g35 g41 L g34 g40 L g32 g40 L g31 g39 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M g11 g39 L g11 g44 L g31 g44 L g32 g43 L g33 g43 L g33 g47 L g35 g47 L g35 g45 L g36 g45 L g38 g46 L g12 g46 L g12 g41 L g38 g41 L g37 g42 L g35 g42 L g35 g41 L g34 g40 L g32 g40 L g31 g39 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M g11 g39 L g31 g39 L g32 g40 L g34 g40 L g35 g41 L g35 g42 L g37 g42 L g38 g41 L g12 g41 L g12 g46 L g38 g46 L g36 g45 L g35 g45 L g35 g47 L g33 g47 L g33 g43 L g32 g43 L g31 g44 L g11 g44 Z","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"actionButtonHome":{"guides":["dx2:*/ ss 3 8","g9:+- vc 0 dx2","g10:+- vc dx2 0","g11:+- hc 0 dx2","g12:+- hc dx2 0","g13:*/ ss 3 4","g14:*/ g13 1 16","g15:*/ g13 1 8","g16:*/ g13 3 16","g17:*/ g13 5 16","g18:*/ g13 7 16","g19:*/ g13 9 16","g20:*/ g13 11 16","g21:*/ g13 3 4","g22:*/ g13 13 16","g23:*/ g13 7 8","g24:+- g9 g14 0","g25:+- g9 g16 0","g26:+- g9 g17 0","g27:+- g9 g21 0","g28:+- g11 g15 0","g29:+- g11 g18 0","g30:+- g11 g19 0","g31:+- g11 g20 0","g32:+- g11 g22 0","g33:+- g11 g23 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L r t L r b L l b Z M hc g9 L g11 vc L g28 vc L g28 g10 L g33 g10 L g33 vc L g12 vc L g32 g26 L g32 g24 L g31 g24 L g31 g25 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M g32 g26 L g32 g24 L g31 g24 L g31 g25 Z M g28 vc L g28 g10 L g29 g10 L g29 g27 L g30 g27 L g30 g10 L g33 g10 L g33 vc Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M hc g9 L g11 vc L g12 vc Z M g29 g27 L g30 g27 L g30 g10 L g29 g10 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M hc g9 L g31 g25 L g31 g24 L g32 g24 L g32 g26 L g12 vc L g33 vc L g33 g10 L g28 g10 L g28 vc L g11 vc Z M g31 g25 L g32 g26 M g33 vc L g28 vc M g29 g10 L g29 g27 L g30 g27 L g30 g10","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"leftRightRibbon":{"guides":["a3:pin 0 adj3 33333","maxAdj1:+- 100000 0 a3","a1:pin 0 adj1 maxAdj1","w1:+- wd2 0 wd32","maxAdj2:*/ 100000 w1 ss","a2:pin 0 adj2 maxAdj2","x1:*/ ss a2 100000","x4:+- r 0 x1","dy1:*/ h a1 200000","dy2:*/ h a3 -200000","ly1:+- vc dy2 dy1","ry4:+- vc dy1 dy2","ly2:+- ly1 dy1 0","ry3:+- b 0 ly2","ly4:*/ ly2 2 1","ry1:+- b 0 ly4","ly3:+- ly4 0 ly1","ry2:+- b 0 ly3","hR:*/ a3 ss 400000","x2:+- hc 0 wd32","x3:+- hc wd32 0","y1:+- ly1 hR 0","y2:+- ry2 0 hR"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l ly2 L x1 t L x1 ly1 L hc ly1 A wd32 hR 3cd4 cd2 A wd32 hR 3cd4 -10800000 L x4 ry2 L x4 ry1 L r ry3 L x4 b L x4 ry4 L hc ry4 A wd32 hR cd4 cd4 L x2 ly3 L x1 ly3 L x1 ly4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x3 y1 A wd32 hR 0 cd4 A wd32 hR 3cd4 -10800000 L x3 ry2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l ly2 L x1 t L x1 ly1 L hc ly1 A wd32 hR 3cd4 cd2 A wd32 hR 3cd4 -10800000 L x4 ry2 L x4 ry1 L r ry3 L x4 b L x4 ry4 L hc ry4 A wd32 hR cd4 cd4 L x2 ly3 L x1 ly3 L x1 ly4 Z M x3 y1 L x3 ry2 M x2 y2 L x2 ly3","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000","adj3:val 16667"]},"flowChartInputOutput":{"guides":["x3:*/ w 2 5","x4:*/ w 3 5","x5:*/ w 4 5","x6:*/ w 9 10"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":5,"path":"M 0 5 L 1 0 L 5 0 L 4 5 Z","stroked":true,"w":5,"windingRule":1}],"adjusts":[]},"snip1Rect":{"guides":["a:pin 0 adj 50000","dx1:*/ ss a 100000","x1:+- r 0 dx1","it:*/ dx1 1 2","ir:+/ x1 r 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l t L x1 t L r dx1 L r b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 16667"]},"lineInv":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L r t","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"upDownArrow":{"guides":["maxAdj2:*/ 50000 h ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","y2:*/ ss a2 100000","y3:+- b 0 y2","dx1:*/ w a1 200000","x1:+- hc 0 dx1","x2:+- hc dx1 0","dy1:*/ x1 y2 wd2","y1:+- y2 0 dy1","y4:+- y3 dy1 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L hc t L r y2 L x2 y2 L x2 y3 L r y3 L hc b L l y3 L x1 y3 L x1 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"star24":{"guides":["a:pin 0 adj 50000","dx1:cos wd2 900000","dx2:cos wd2 1800000","dx3:cos wd2 2700000","dx4:val wd4","dx5:cos wd2 4500000","dy1:sin hd2 4500000","dy2:sin hd2 3600000","dy3:sin hd2 2700000","dy4:val hd4","dy5:sin hd2 900000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc 0 dx3","x4:+- hc 0 dx4","x5:+- hc 0 dx5","x6:+- hc dx5 0","x7:+- hc dx4 0","x8:+- hc dx3 0","x9:+- hc dx2 0","x10:+- hc dx1 0","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc 0 dy3","y4:+- vc 0 dy4","y5:+- vc 0 dy5","y6:+- vc dy5 0","y7:+- vc dy4 0","y8:+- vc dy3 0","y9:+- vc dy2 0","y10:+- vc dy1 0","iwd2:*/ wd2 a 50000","ihd2:*/ hd2 a 50000","sdx1:*/ iwd2 99144 100000","sdx2:*/ iwd2 92388 100000","sdx3:*/ iwd2 79335 100000","sdx4:*/ iwd2 60876 100000","sdx5:*/ iwd2 38268 100000","sdx6:*/ iwd2 13053 100000","sdy1:*/ ihd2 99144 100000","sdy2:*/ ihd2 92388 100000","sdy3:*/ ihd2 79335 100000","sdy4:*/ ihd2 60876 100000","sdy5:*/ ihd2 38268 100000","sdy6:*/ ihd2 13053 100000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc 0 sdx3","sx4:+- hc 0 sdx4","sx5:+- hc 0 sdx5","sx6:+- hc 0 sdx6","sx7:+- hc sdx6 0","sx8:+- hc sdx5 0","sx9:+- hc sdx4 0","sx10:+- hc sdx3 0","sx11:+- hc sdx2 0","sx12:+- hc sdx1 0","sy1:+- vc 0 sdy1","sy2:+- vc 0 sdy2","sy3:+- vc 0 sdy3","sy4:+- vc 0 sdy4","sy5:+- vc 0 sdy5","sy6:+- vc 0 sdy6","sy7:+- vc sdy6 0","sy8:+- vc sdy5 0","sy9:+- vc sdy4 0","sy10:+- vc sdy3 0","sy11:+- vc sdy2 0","sy12:+- vc sdy1 0","idx:cos iwd2 2700000","idy:sin ihd2 2700000","il:+- hc 0 idx","it:+- vc 0 idy","ir:+- hc idx 0","ib:+- vc idy 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L sx1 sy6 L x1 y5 L sx2 sy5 L x2 y4 L sx3 sy4 L x3 y3 L sx4 sy3 L x4 y2 L sx5 sy2 L x5 y1 L sx6 sy1 L hc t L sx7 sy1 L x6 y1 L sx8 sy2 L x7 y2 L sx9 sy3 L x8 y3 L sx10 sy4 L x9 y4 L sx11 sy5 L x10 y5 L sx12 sy6 L r vc L sx12 sy7 L x10 y6 L sx11 sy8 L x9 y7 L sx10 sy9 L x8 y8 L sx9 sy10 L x7 y9 L sx8 sy11 L x6 y10 L sx7 sy12 L hc b L sx6 sy12 L x5 y10 L sx5 sy11 L x4 y9 L sx4 sy10 L x3 y8 L sx3 sy9 L x2 y7 L sx2 sy8 L x1 y6 L sx1 sy7 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 37500"]},"dodecagon":{"guides":["x1:*/ w 2894 21600","x2:*/ w 7906 21600","x3:*/ w 13694 21600","x4:*/ w 18706 21600","y1:*/ h 2894 21600","y2:*/ h 7906 21600","y3:*/ h 13694 21600","y4:*/ h 18706 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L x1 y1 L x2 t L x3 t L x4 y1 L r y2 L r y3 L x4 y4 L x3 b L x2 b L x1 y4 L l y3 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"wedgeRoundRectCallout":{"guides":["dxPos:*/ w adj1 100000","dyPos:*/ h adj2 100000","xPos:+- hc dxPos 0","yPos:+- vc dyPos 0","dq:*/ dxPos h w","ady:abs dyPos","adq:abs dq","dz:+- ady 0 adq","xg1:?: dxPos 7 2","xg2:?: dxPos 10 5","x1:*/ w xg1 12","x2:*/ w xg2 12","yg1:?: dyPos 7 2","yg2:?: dyPos 10 5","y1:*/ h yg1 12","y2:*/ h yg2 12","t1:?: dxPos l xPos","xl:?: dz l t1","t2:?: dyPos x1 xPos","xt:?: dz t2 x1","t3:?: dxPos xPos r","xr:?: dz r t3","t4:?: dyPos xPos x1","xb:?: dz t4 x1","t5:?: dxPos y1 yPos","yl:?: dz y1 t5","t6:?: dyPos t yPos","yt:?: dz t6 t","t7:?: dxPos yPos y1","yr:?: dz y1 t7","t8:?: dyPos yPos b","yb:?: dz t8 b","u1:*/ ss adj3 100000","u2:+- r 0 u1","v2:+- b 0 u1","il:*/ u1 29289 100000","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l u1 A u1 u1 cd2 cd4 L x1 t L xt yt L x2 t L u2 t A u1 u1 3cd4 cd4 L r y1 L xr yr L r y2 L r v2 A u1 u1 0 cd4 L x2 b L xb yb L x1 b L u1 b A u1 u1 cd4 cd4 L l y2 L xl yl L l y1 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val -20833","adj2:val 62500","adj3:val 16667"]},"round2DiagRect":{"guides":["a1:pin 0 adj1 50000","a2:pin 0 adj2 50000","x1:*/ ss a1 100000","y1:+- b 0 x1","a:*/ ss a2 100000","x2:+- r 0 a","y2:+- b 0 a","dx1:*/ x1 29289 100000","dx2:*/ a 29289 100000","d:+- dx1 0 dx2","dx:?: d dx1 dx2","ir:+- r 0 dx","ib:+- b 0 dx"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 t L x2 t A a a 3cd4 cd4 L r y1 A x1 x1 0 cd4 L a b A a a cd4 cd4 L l x1 A x1 x1 cd2 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16667","adj2:val 0"]},"flowChartMagneticDrum":{"guides":["x2:*/ w 2 3"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":6,"path":"M 1 0 L 5 0 A 1 3 3cd4 cd2 L 1 6 A 1 3 cd4 cd2 Z","stroked":false,"w":6,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":6,"path":"M 5 6 A 1 3 cd4 cd2","stroked":true,"w":6,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":6,"path":"M 1 0 L 5 0 A 1 3 3cd4 cd2 L 1 6 A 1 3 cd4 cd2 Z","stroked":true,"w":6,"windingRule":1}],"adjusts":[]},"flowChartPreparation":{"guides":["x2:*/ w 4 5"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":10,"path":"M 0 5 L 2 0 L 8 0 L 10 5 L 8 10 L 2 10 Z","stroked":true,"w":10,"windingRule":1}],"adjusts":[]},"ellipseRibbon2":{"guides":["a1:pin 0 adj1 100000","a2:pin 25000 adj2 75000","q10:+- 100000 0 a1","q11:*/ q10 1 2","q12:+- a1 0 q11","minAdj3:max 0 q12","a3:pin minAdj3 adj3 a1","dx2:*/ w a2 200000","x2:+- hc 0 dx2","x3:+- x2 wd8 0","x4:+- r 0 x3","x5:+- r 0 x2","x6:+- r 0 wd8","dy1:*/ h a3 100000","f1:*/ 4 dy1 w","q1:*/ x3 x3 w","q2:+- x3 0 q1","u1:*/ f1 q2 1","y1:+- b 0 u1","cx1:*/ x3 1 2","cu1:*/ f1 cx1 1","cy1:+- b 0 cu1","cx2:+- r 0 cx1","q1:*/ h a1 100000","dy3:+- q1 0 dy1","q3:*/ x2 x2 w","q4:+- x2 0 q3","q5:*/ f1 q4 1","u3:+- q5 dy3 0","y3:+- b 0 u3","q6:+- dy1 dy3 u3","q7:+- q6 dy1 0","cu3:+- q7 dy3 0","cy3:+- b 0 cu3","rh:+- b 0 q1","q8:*/ dy1 14 16","u2:+/ q8 rh 2","y2:+- b 0 u2","u5:+- q5 rh 0","y5:+- b 0 u5","u6:+- u3 rh 0","y6:+- b 0 u6","cx4:*/ x2 1 2","q9:*/ f1 cx4 1","cu4:+- q9 rh 0","cy4:+- b 0 cu4","cx5:+- r 0 cx4","cu6:+- cu3 rh 0","cy6:+- b 0 cu6","u7:+- u1 dy3 0","y7:+- b 0 u7","cu7:+- q1 q1 u7","cy7:+- b 0 cu7"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b Q cx1 cy1 x3 y1 L x2 y3 Q hc cy3 x5 y3 L x4 y1 Q cx2 cy1 r b L x6 y2 L r q1 Q cx5 cy4 x5 y5 L x5 y6 Q hc cy6 x2 y6 L x2 y5 Q cx4 cy4 l q1 L wd8 y2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M x3 y7 L x3 y1 L x2 y3 Q hc cy3 x5 y3 L x4 y1 L x4 y7 Q hc cy7 x3 y7 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l b L wd8 y2 L l q1 Q cx4 cy4 x2 y5 L x2 y6 Q hc cy6 x5 y6 L x5 y5 Q cx5 cy4 r q1 L x6 y2 L r b Q cx2 cy1 x4 y1 L x5 y3 Q hc cy3 x2 y3 L x3 y1 Q cx1 cy1 l b Z M x2 y3 L x2 y5 M x5 y5 L x5 y3 M x3 y7 L x3 y1 M x4 y1 L x4 y7","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 50000","adj3:val 12500"]},"round2SameRect":{"guides":["a1:pin 0 adj1 50000","a2:pin 0 adj2 50000","tx1:*/ ss a1 100000","tx2:+- r 0 tx1","bx1:*/ ss a2 100000","bx2:+- r 0 bx1","by1:+- b 0 bx1","d:+- tx1 0 bx1","tdx:*/ tx1 29289 100000","bdx:*/ bx1 29289 100000","il:?: d tdx bdx","ir:+- r 0 il","ib:+- b 0 bdx"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M tx1 t L tx2 t A tx1 tx1 3cd4 cd4 L r by1 A bx1 bx1 0 cd4 L bx1 b A bx1 bx1 cd4 cd4 L l tx1 A tx1 tx1 cd2 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 16667","adj2:val 0"]},"mathMinus":{"guides":["a1:pin 0 adj1 100000","dy1:*/ h a1 200000","dx1:*/ w 73490 200000","y1:+- vc 0 dy1","y2:+- vc dy1 0","x1:+- hc 0 dx1","x2:+- hc dx1 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 L x2 y1 L x2 y2 L x1 y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 23520"]},"blockArc":{"guides":["stAng:pin 0 adj1 21599999","istAng:pin 0 adj2 21599999","a3:pin 0 adj3 50000","sw11:+- istAng 0 stAng","sw12:+- sw11 21600000 0","swAng:?: sw11 sw11 sw12","iswAng:+- 0 0 swAng","wt1:sin wd2 stAng","ht1:cos hd2 stAng","wt3:sin wd2 istAng","ht3:cos hd2 istAng","dx1:cat2 wd2 ht1 wt1","dy1:sat2 hd2 ht1 wt1","dx3:cat2 wd2 ht3 wt3","dy3:sat2 hd2 ht3 wt3","x1:+- hc dx1 0","y1:+- vc dy1 0","x3:+- hc dx3 0","y3:+- vc dy3 0","dr:*/ ss a3 100000","iwd2:+- wd2 0 dr","ihd2:+- hd2 0 dr","wt2:sin iwd2 istAng","ht2:cos ihd2 istAng","wt4:sin iwd2 stAng","ht4:cos ihd2 stAng","dx2:cat2 iwd2 ht2 wt2","dy2:sat2 ihd2 ht2 wt2","dx4:cat2 iwd2 ht4 wt4","dy4:sat2 ihd2 ht4 wt4","x2:+- hc dx2 0","y2:+- vc dy2 0","x4:+- hc dx4 0","y4:+- vc dy4 0","sw0:+- 21600000 0 stAng","da1:+- swAng 0 sw0","g1:max x1 x2","g2:max x3 x4","g3:max g1 g2","ir:?: da1 r g3","sw1:+- cd4 0 stAng","sw2:+- 27000000 0 stAng","sw3:?: sw1 sw1 sw2","da2:+- swAng 0 sw3","g5:max y1 y2","g6:max y3 y4","g7:max g5 g6","ib:?: da2 b g7","sw4:+- cd2 0 stAng","sw5:+- 32400000 0 stAng","sw6:?: sw4 sw4 sw5","da3:+- swAng 0 sw6","g9:min x1 x2","g10:min x3 x4","g11:min g9 g10","il:?: da3 l g11","sw7:+- 3cd4 0 stAng","sw8:+- 37800000 0 stAng","sw9:?: sw7 sw7 sw8","da4:+- swAng 0 sw9","g13:min y1 y2","g14:min y3 y4","g15:min g13 g14","it:?: da4 t g15","x5:+/ x1 x4 2","y5:+/ y1 y4 2","x6:+/ x3 x2 2","y6:+/ y3 y2 2","cang1:+- stAng 0 cd4","cang2:+- istAng cd4 0","cang3:+/ cang1 cang2 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 A wd2 hd2 stAng swAng L x2 y2 A iwd2 ihd2 istAng iswAng Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 10800000","adj2:val 0","adj3:val 25000"]},"flowChartPredefinedProcess":{"guides":["x2:*/ w 7 8"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":1,"path":"M 0 0 L 1 0 L 1 1 L 0 1 Z","stroked":false,"w":1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":8,"path":"M 1 0 L 1 8 M 7 0 L 7 8","stroked":true,"w":8,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":1,"path":"M 0 0 L 1 0 L 1 1 L 0 1 Z","stroked":true,"w":1,"windingRule":1}],"adjusts":[]},"flowChartMagneticDisk":{"guides":["y3:*/ h 5 6"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":6,"path":"M 0 1 A 3 1 cd2 cd2 L 6 5 A 3 1 0 cd2 Z","stroked":false,"w":6,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":6,"path":"M 6 1 A 3 1 0 cd2","stroked":true,"w":6,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":6,"path":"M 0 1 A 3 1 cd2 cd2 L 6 5 A 3 1 0 cd2 Z","stroked":true,"w":6,"windingRule":1}],"adjusts":[]},"flowChartOr":{"guides":["idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M hc t L hc b M l vc L r vc","stroked":true,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l vc A wd2 hd2 cd2 cd4 A wd2 hd2 3cd4 cd4 A wd2 hd2 0 cd4 A wd2 hd2 cd4 cd4 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"chartX":{"guides":[],"paths":[{"extrusionOk":false,"fill":"NONE","filled":false,"h":10,"path":"M 0 0 L 10 10 M 0 10 L 10 0","stroked":true,"w":10,"windingRule":1},{"extrusionOk":false,"fill":"NORM","filled":true,"h":10,"path":"M 0 0 L 0 10 L 10 10 L 10 0 Z","stroked":false,"w":10,"windingRule":1}],"adjusts":[]},"flowChartOffpageConnector":{"guides":["y1:*/ h 4 5"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":10,"path":"M 0 0 L 10 0 L 10 8 L 5 10 L 0 8 Z","stroked":true,"w":10,"windingRule":1}],"adjusts":[]},"bevel":{"guides":["a:pin 0 adj 50000","x1:*/ ss a 100000","x2:+- r 0 x1","y2:+- b 0 x1"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 x1 L x2 x1 L x2 y2 L x1 y2 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"LIGHTEN_LESS","filled":true,"h":-1,"path":"M l t L r t L x2 x1 L x1 x1 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN_LESS","filled":true,"h":-1,"path":"M l b L x1 y2 L x2 y2 L r b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"LIGHTEN","filled":true,"h":-1,"path":"M l t L x1 x1 L x1 y2 L l b Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"DARKEN","filled":true,"h":-1,"path":"M r t L r b L x2 y2 L x2 x1 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M l t L r t L r b L l b Z M x1 x1 L x2 x1 L x2 y2 L x1 y2 Z M l t L x1 x1 M l b L x1 y2 M r t L x2 x1 M r b L x2 y2","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 12500"]},"rightArrow":{"guides":["maxAdj2:*/ 100000 w ss","a1:pin 0 adj1 100000","a2:pin 0 adj2 maxAdj2","dx1:*/ ss a2 100000","x1:+- r 0 dx1","dy1:*/ h a1 200000","y1:+- vc 0 dy1","y2:+- vc dy1 0","dx2:*/ y1 dx1 hd2","x2:+- x1 dx2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y1 L x1 y1 L x1 t L r vc L x1 b L x1 y2 L l y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 50000","adj2:val 50000"]},"bentArrow":{"guides":["a2:pin 0 adj2 50000","maxAdj1:*/ a2 2 1","a1:pin 0 adj1 maxAdj1","a3:pin 0 adj3 50000","th:*/ ss a1 100000","aw2:*/ ss a2 100000","th2:*/ th 1 2","dh2:+- aw2 0 th2","ah:*/ ss a3 100000","bw:+- r 0 ah","bh:+- b 0 dh2","bs:min bw bh","maxAdj4:*/ 100000 bs ss","a4:pin 0 adj4 maxAdj4","bd:*/ ss a4 100000","bd3:+- bd 0 th","bd2:max bd3 0","x3:+- th bd2 0","x4:+- r 0 ah","y3:+- dh2 th 0","y4:+- y3 dh2 0","y5:+- dh2 bd 0","y6:+- y3 bd2 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l b L l y5 A bd bd cd2 cd4 L x4 dh2 L x4 t L r aw2 L x4 y4 L x4 y3 L x3 y3 A bd2 bd2 3cd4 -5400000 L th b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000","adj4:val 43750"]},"diamond":{"guides":["ir:*/ w 3 4","ib:*/ h 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L hc t L r vc L hc b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":[]},"bracePair":{"guides":["a:pin 0 adj 25000","x1:*/ ss a 100000","x2:*/ ss a 50000","x3:+- r 0 x2","x4:+- r 0 x1","y2:+- vc 0 x1","y3:+- vc x1 0","y4:+- b 0 x1","it:*/ x1 29289 100000","il:+- x1 it 0","ir:+- r 0 il","ib:+- b 0 it"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x2 b A x1 x1 cd4 cd4 L x1 y3 A x1 x1 0 -5400000 A x1 x1 cd4 -5400000 L x1 x1 A x1 x1 cd2 cd4 L x3 t A x1 x1 3cd4 cd4 L x4 y2 A x1 x1 cd2 -5400000 A x1 x1 3cd4 -5400000 L x4 y4 A x1 x1 0 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x2 b A x1 x1 cd4 cd4 L x1 y3 A x1 x1 0 -5400000 A x1 x1 cd4 -5400000 L x1 x1 A x1 x1 cd2 cd4 M x3 t A x1 x1 3cd4 cd4 L x4 y2 A x1 x1 cd2 -5400000 A x1 x1 3cd4 -5400000 L x4 y4 A x1 x1 0 cd4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 8333"]},"flowChartDocument":{"guides":["y1:*/ h 17322 21600","y2:*/ h 20172 21600"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":21600,"path":"M 0 0 L 21600 0 L 21600 17322 C 10800 17322 10800 23922 0 20172 Z","stroked":true,"w":21600,"windingRule":1}],"adjusts":[]},"bentUpArrow":{"guides":["a1:pin 0 adj1 50000","a2:pin 0 adj2 50000","a3:pin 0 adj3 50000","y1:*/ ss a3 100000","dx1:*/ ss a2 50000","x1:+- r 0 dx1","dx3:*/ ss a2 100000","x3:+- r 0 dx3","dx2:*/ ss a1 200000","x2:+- x3 0 dx2","x4:+- x3 dx2 0","dy2:*/ ss a1 100000","y2:+- b 0 dy2","x0:*/ x4 1 2","y3:+/ y2 b 2","y15:+/ y1 b 2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l y2 L x2 y2 L x2 y1 L x1 y1 L x3 t L r y1 L x4 y1 L x4 b L l b Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 25000","adj2:val 25000","adj3:val 25000"]},"octagon":{"guides":["a:pin 0 adj 50000","x1:*/ ss a 100000","x2:+- r 0 x1","y2:+- b 0 x1","il:*/ x1 1 2","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l x1 L x1 t L x2 t L r x1 L r y2 L x2 b L x1 b L l y2 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 29289"]},"chord":{"guides":["stAng:pin 0 adj1 21599999","enAng:pin 0 adj2 21599999","sw1:+- enAng 0 stAng","sw2:+- sw1 21600000 0","swAng:?: sw1 sw1 sw2","wt1:sin wd2 stAng","ht1:cos hd2 stAng","dx1:cat2 wd2 ht1 wt1","dy1:sat2 hd2 ht1 wt1","wt2:sin wd2 enAng","ht2:cos hd2 enAng","dx2:cat2 wd2 ht2 wt2","dy2:sat2 hd2 ht2 wt2","x1:+- hc dx1 0","y1:+- vc dy1 0","x2:+- hc dx2 0","y2:+- vc dy2 0","x3:+/ x1 x2 2","y3:+/ y1 y2 2","midAng0:*/ swAng 1 2","midAng:+- stAng midAng0 cd2","idx:cos wd2 2700000","idy:sin hd2 2700000","il:+- hc 0 idx","ir:+- hc idx 0","it:+- vc 0 idy","ib:+- vc idy 0"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M x1 y1 A wd2 hd2 stAng swAng Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj1:val 2700000","adj2:val 16200000"]},"flowChartCollate":{"guides":["ir:*/ w 3 4","ib:*/ h 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":2,"path":"M 0 0 L 2 0 L 1 1 L 2 2 L 0 2 L 1 1 Z","stroked":true,"w":2,"windingRule":1}],"adjusts":[]},"flowChartExtract":{"guides":["x2:*/ w 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":2,"path":"M 0 2 L 1 0 L 2 2 Z","stroked":true,"w":2,"windingRule":1}],"adjusts":[]},"star32":{"guides":["a:pin 0 adj 50000","dx1:*/ wd2 98079 100000","dx2:*/ wd2 92388 100000","dx3:*/ wd2 83147 100000","dx4:cos wd2 2700000","dx5:*/ wd2 55557 100000","dx6:*/ wd2 38268 100000","dx7:*/ wd2 19509 100000","dy1:*/ hd2 98079 100000","dy2:*/ hd2 92388 100000","dy3:*/ hd2 83147 100000","dy4:sin hd2 2700000","dy5:*/ hd2 55557 100000","dy6:*/ hd2 38268 100000","dy7:*/ hd2 19509 100000","x1:+- hc 0 dx1","x2:+- hc 0 dx2","x3:+- hc 0 dx3","x4:+- hc 0 dx4","x5:+- hc 0 dx5","x6:+- hc 0 dx6","x7:+- hc 0 dx7","x8:+- hc dx7 0","x9:+- hc dx6 0","x10:+- hc dx5 0","x11:+- hc dx4 0","x12:+- hc dx3 0","x13:+- hc dx2 0","x14:+- hc dx1 0","y1:+- vc 0 dy1","y2:+- vc 0 dy2","y3:+- vc 0 dy3","y4:+- vc 0 dy4","y5:+- vc 0 dy5","y6:+- vc 0 dy6","y7:+- vc 0 dy7","y8:+- vc dy7 0","y9:+- vc dy6 0","y10:+- vc dy5 0","y11:+- vc dy4 0","y12:+- vc dy3 0","y13:+- vc dy2 0","y14:+- vc dy1 0","iwd2:*/ wd2 a 50000","ihd2:*/ hd2 a 50000","sdx1:*/ iwd2 99518 100000","sdx2:*/ iwd2 95694 100000","sdx3:*/ iwd2 88192 100000","sdx4:*/ iwd2 77301 100000","sdx5:*/ iwd2 63439 100000","sdx6:*/ iwd2 47140 100000","sdx7:*/ iwd2 29028 100000","sdx8:*/ iwd2 9802 100000","sdy1:*/ ihd2 99518 100000","sdy2:*/ ihd2 95694 100000","sdy3:*/ ihd2 88192 100000","sdy4:*/ ihd2 77301 100000","sdy5:*/ ihd2 63439 100000","sdy6:*/ ihd2 47140 100000","sdy7:*/ ihd2 29028 100000","sdy8:*/ ihd2 9802 100000","sx1:+- hc 0 sdx1","sx2:+- hc 0 sdx2","sx3:+- hc 0 sdx3","sx4:+- hc 0 sdx4","sx5:+- hc 0 sdx5","sx6:+- hc 0 sdx6","sx7:+- hc 0 sdx7","sx8:+- hc 0 sdx8","sx9:+- hc sdx8 0","sx10:+- hc sdx7 0","sx11:+- hc sdx6 0","sx12:+- hc sdx5 0","sx13:+- hc sdx4 0","sx14:+- hc sdx3 0","sx15:+- hc sdx2 0","sx16:+- hc sdx1 0","sy1:+- vc 0 sdy1","sy2:+- vc 0 sdy2","sy3:+- vc 0 sdy3","sy4:+- vc 0 sdy4","sy5:+- vc 0 sdy5","sy6:+- vc 0 sdy6","sy7:+- vc 0 sdy7","sy8:+- vc 0 sdy8","sy9:+- vc sdy8 0","sy10:+- vc sdy7 0","sy11:+- vc sdy6 0","sy12:+- vc sdy5 0","sy13:+- vc sdy4 0","sy14:+- vc sdy3 0","sy15:+- vc sdy2 0","sy16:+- vc sdy1 0","idx:cos iwd2 2700000","idy:sin ihd2 2700000","il:+- hc 0 idx","it:+- vc 0 idy","ir:+- hc idx 0","ib:+- vc idy 0","yAdj:+- vc 0 ihd2"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l vc L sx1 sy8 L x1 y7 L sx2 sy7 L x2 y6 L sx3 sy6 L x3 y5 L sx4 sy5 L x4 y4 L sx5 sy4 L x5 y3 L sx6 sy3 L x6 y2 L sx7 sy2 L x7 y1 L sx8 sy1 L hc t L sx9 sy1 L x8 y1 L sx10 sy2 L x9 y2 L sx11 sy3 L x10 y3 L sx12 sy4 L x11 y4 L sx13 sy5 L x12 y5 L sx14 sy6 L x13 y6 L sx15 sy7 L x14 y7 L sx16 sy8 L r vc L sx16 sy9 L x14 y8 L sx15 sy10 L x13 y9 L sx14 sy11 L x12 y10 L sx13 sy12 L x11 y11 L sx12 sy13 L x10 y12 L sx11 sy14 L x9 y13 L sx10 sy15 L x8 y14 L sx9 sy16 L hc b L sx8 sy16 L x7 y14 L sx7 sy15 L x6 y13 L sx6 sy14 L x5 y12 L sx5 sy13 L x4 y11 L sx4 sy12 L x3 y10 L sx3 sy11 L x2 y9 L sx2 sy10 L x1 y8 L sx1 sy9 Z","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 37500"]},"bracketPair":{"guides":["a:pin 0 adj 50000","x1:*/ ss a 100000","x2:+- r 0 x1","y2:+- b 0 x1","il:*/ x1 29289 100000","ir:+- r 0 il","ib:+- b 0 il"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":-1,"path":"M l x1 A x1 x1 cd2 cd4 L x2 t A x1 x1 3cd4 cd4 L r y2 A x1 x1 0 cd4 L x1 b A x1 x1 cd4 cd4 Z","stroked":false,"w":-1,"windingRule":1},{"extrusionOk":false,"fill":"NONE","filled":false,"h":-1,"path":"M x1 b A x1 x1 cd4 cd4 L l x1 A x1 x1 cd2 cd4 M x2 t A x1 x1 3cd4 cd4 L r y2 A x1 x1 0 cd4","stroked":true,"w":-1,"windingRule":1}],"adjusts":["adj:val 16667"]},"flowChartMerge":{"guides":["x2:*/ w 3 4"],"paths":[{"extrusionOk":false,"fill":"NORM","filled":true,"h":2,"path":"M 0 0 L 2 0 L 1 2 Z","stroked":true,"w":2,"windingRule":1}],"adjusts":[]}};

function geometryPaths(property, zoom) {
    zoom = zoom || 1
    let geometry = property.geometry
    let anchor = property.anchor ? [...property.anchor.map(s => s * zoom)] : property.anchor
    if (!geometry || !anchor || (anchor[2] == 0 && anchor[3] == 0)) {
        return []
    }
    let _anchor = [0, 0, anchor[2], anchor[3]]
    if (geometry.name == 'custom') {
        return getPaths(geometry.data.paths, true, _anchor, anchor, zoom)
    } else if (geometry.name == 'line' || geometry.name == 'straightConnector1') {
        let paths = [{ path: `M 0 0 L ${_anchor[2]} ${_anchor[3]}`, stroked: true, filled: true, w: -1, h: -1 }]
        return getPaths(paths, false, _anchor, anchor)
    } else {
        let paths = calcPreGeometryPaths(geometry, _anchor)
        return getPaths(paths, true, _anchor, anchor)
    }
}

function getPaths(paths, isEmu, anchor, real_anchor, zoom) {
    let resultPaths = []
    for (let i = 0; i < paths.length; i++) {
        let path = paths[i]
        // console.log('path: ', path)
        let scaleX = 1, scaleY = 1
        if (path.w > 0 && path.h > 0) {
            if (path.w < 2000 && path.h < 2000) {
                isEmu = false
            }
            let w = value2px(path.w, isEmu)
            let h = value2px(path.h, isEmu)
            scaleX = anchor[2] / w
            scaleY = anchor[3] / h
        } else if (zoom) {
            scaleX = zoom
            scaleY = zoom
        }
        let newPaths = []
        let split = path.path.replace(/\s+/g, ' ').trim().split(' ')
        for (let j = 0; j < split.length; j++) {
            switch (split[j]) {
                case 'M':
                case 'L':
                    newPaths.push(split[j])
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    break
                case 'Q':
                    newPaths.push('Q')
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    break
                case 'C':
                    newPaths.push('C')
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    newPaths.push(value2px(split[++j], isEmu))
                    break
                case 'Z':
                    newPaths.push('Z')
                    break
            }
        }
        resultPaths.push({
            scaleX,
            scaleY,
            x: real_anchor[0],
            y: real_anchor[1],
            path: newPaths.join(' '),
            stroked: path.stroked,
            filled: path.filled
        })
    }
    return resultPaths
}

function calcPreGeometryPaths(geometry, anchor) {
    let pre = geometryMap[geometry.name]
    if (!pre) {
        console.log('未知形状：' + geometry.name)
        return []
    }
    // console.log(geometry.name)
    let values = {}
    let obj = {
        x: value2emu(anchor[0]),
        y: value2emu(anchor[1]),
        width: value2emu(anchor[2]),
        height: value2emu(anchor[3]),
        centerX: value2emu(anchor[0] + anchor[2] / 2),
        centerY: value2emu(anchor[1] + anchor[3] / 2),
        maxX: value2emu(anchor[0] + anchor[2]),
        maxY: value2emu(anchor[1] + anchor[3]),
        values
    }
    if (pre.adjusts) {
        for (let i = 0; i < pre.adjusts.length; i++) {
            let adjust = pre.adjusts[i]
            let idx = adjust.indexOf(':')
            let name = adjust.substring(0, idx)
            let fmla = adjust.substring(idx + 1)
            let value = fmlaEvaluate(obj, fmla)
            values[name] = value
        }
    }
    if (geometry.avLst) {
        for (let i = 0; i < geometry.avLst.length; i++) {
            let adjust = geometry.avLst[i]
            let idx = adjust.indexOf(':')
            let name = adjust.substring(0, idx)
            let fmla = adjust.substring(idx + 1)
            let value = fmlaEvaluate(obj, fmla)
            values[name] = value
        }
    }
    if (pre.guides) {
        for (let i = 0; i < pre.guides.length; i++) {
            let adjust = pre.guides[i]
            let idx = adjust.indexOf(':')
            let name = adjust.substring(0, idx)
            let fmla = adjust.substring(idx + 1)
            let value = fmlaEvaluate(obj, fmla)
            values[name] = value
        }
    }
    let paths = pre.paths
    let newPaths = []
    for (let i = 0; i < paths.length; i++) {
        let path = { ...paths[i] }
        let split = path.path.replace(/\s+/g, ' ').trim().split(' ')
        let currentPoint = { x: obj.x, y: obj.y }
        let newPath = []
        for (let j = 0; j < split.length; j++) {
            switch (split[j]) {
                case 'A':
                    let wr = pathEvaluate(obj, split[++j])
                    let hr = pathEvaluate(obj, split[++j])
                    let stAng = pathEvaluate(obj, split[++j])
                    let swAng = pathEvaluate(obj, split[++j])
                    let _paths = arcToBezierCurve(currentPoint, wr, hr, stAng, swAng)
                    // console.log('A: ', _paths)
                    let p = 0
                    if (_paths[0] == 'M' && newPath.length > 0 && newPath[newPath.length - 1] != 'Z' &&
                        valueEquals(newPath[newPath.length - 2], _paths[1]) && valueEquals(newPath[newPath.length - 1], _paths[2])) {
                        p = 3
                    }
                    for (; p < _paths.length; p++) {
                        newPath.push(_paths[p])
                    }
                    currentPoint.x = _paths[_paths.length - 2]
                    currentPoint.y = _paths[_paths.length - 1]
                    break
                case 'M':
                case 'L':
                    newPath.push(split[j])
                    currentPoint.x = pathEvaluate(obj, split[++j])
                    currentPoint.y = pathEvaluate(obj, split[++j])
                    newPath.push(currentPoint.x)
                    newPath.push(currentPoint.y)
                    break
                case 'Q':
                    newPath.push(split[j])
                    newPath.push(pathEvaluate(obj, split[++j]))
                    newPath.push(pathEvaluate(obj, split[++j]))
                    currentPoint.x = pathEvaluate(obj, split[++j])
                    currentPoint.y = pathEvaluate(obj, split[++j])
                    newPath.push(currentPoint.x)
                    newPath.push(currentPoint.y)
                    break
                case 'C':
                    newPath.push(split[j])
                    newPath.push(pathEvaluate(obj, split[++j]))
                    newPath.push(pathEvaluate(obj, split[++j]))
                    newPath.push(pathEvaluate(obj, split[++j]))
                    newPath.push(pathEvaluate(obj, split[++j]))
                    currentPoint.x = pathEvaluate(obj, split[++j])
                    currentPoint.y = pathEvaluate(obj, split[++j])
                    newPath.push(currentPoint.x)
                    newPath.push(currentPoint.y)
                    break
                case 'Z':
                    newPath.push(split[j])
                    break
            }
        }
        path.path = newPath.join(' ')
        newPaths.push(path)
    }
    return newPaths
}

function pathEvaluate(obj, value) {
    if (obj.values && obj.values[value] != null) {
        return obj.values[value]
    }
    let ss = Math.min(obj.width, obj.height)
    switch (value) {
        case 'l':
            return obj.x
        case 't':
            return obj.y
        case 'b':
            return obj.maxY
        case 'r':
            return obj.maxX
        case 'hc':
            return obj.centerX
        case 'vc':
            return obj.centerY
        case '3cd4':
            return 1.62E7
        case '3cd8':
            return 8100000
        case '5cd8':
            return 1.35E7
        case '7cd8':
            return 1.89E7
        case 'cd2':
            return 1.08E7
        case 'cd4':
            return 5400000
        case 'cd8':
            return 2700000
        case 'ss':
            return ss
        case 'ssd2':
            return ss / 2
        case 'ssd4':
            return ss / 4
        case 'ssd6':
            return ss / 6
        case 'ssd8':
            return ss / 8
        case 'ssd16':
            return ss / 16
        case 'ssd32':
            return ss / 32
        case 'ls':
            return Math.max(obj.width, obj.height)
        case 'w':
            return obj.width
        case 'wd2':
            return obj.width / 2
        case 'wd3':
            return obj.width / 3
        case 'wd4':
            return obj.width / 4
        case 'wd5':
            return obj.width / 5
        case 'wd6':
            return obj.width / 6
        case 'wd8':
            return obj.width / 8
        case 'wd10':
            return obj.width / 10
        case 'wd32':
            return obj.width / 32
        case 'h':
            return obj.height
        case 'hd2':
            return obj.height / 2
        case 'hd3':
            return obj.height / 3
        case 'hd4':
            return obj.height / 4
        case 'hd5':
            return obj.height / 5
        case 'hd6':
            return obj.height / 6
        case 'hd7':
            return obj.height / 7
        case 'hd8':
            return obj.height / 8
        default:
            if (/^(-)?(\d+)(\.)?(\d+)?$/.test(value)) {
                return +value
            } else {
                console.log('未知pathEval: ' + value)
                return 0
            }
    }
}

function fmlaEvaluate(obj, fmla) {
    let split = fmla.replace(/\s+/g, ' ').trim().split(' ')
    let op = split[0]
    let x = split.length > 1 ? pathEvaluate(obj, split[1]) : 0
    let y = split.length > 2 ? pathEvaluate(obj, split[2]) : 0
    let z = split.length > 3 ? pathEvaluate(obj, split[3]) : 0
    switch (op) {
        case 'abs':
            return Math.abs(x)
        case '+/':
        case 'adddiv':
            return z == 0 ? 0 : (x + y) / z
        case '+-':
        case 'addsub':
            return x + y - z
        case 'at2':
            return (Math.atan2(y, x) * 180 / Math.PI) * 60000
        case 'cos':
            return x * Math.cos(y / 60000 / 180 * Math.PI)
        case 'cat2':
            return x * Math.cos(Math.atan2(z, y))
        case '?:':
        case 'ifelse':
            return x > 0 ? y : z
        case 'val':
            return x
        case 'max':
            return Math.max(x, y)
        case 'min':
            return Math.min(x, y)
        case 'mod':
            return Math.sqrt(x * x + y * y + z * z)
        case '*/':
        case 'muldiv':
            return z == 0 ? 0 : x * y / z
        case 'pin':
            return Math.max(x, Math.min(y, z))
        case 'sat2':
            return x * Math.sin(Math.atan2(z, y))
        case 'sin':
            return x * Math.sin(y / 60000 / 180 * Math.PI)
        case 'sqrt':
            return Math.sqrt(x)
        case 'tan':
            return x * Math.tan(y / 60000 / 180 * Math.PI)
        default:
            console.log('未知op: ' + op, fmla)
            return 0
    }
}

function arcToBezierCurve(pt, wr, hr, stAng, swAng) {
    let rx = wr
    let ry = hr
    let ooStart = stAng / 60000
    let ooExtent = swAng / 60000
    let start = convertOoxml2Angle(ooStart, rx, ry)
    let extent = convertOoxml2Angle(ooStart + ooExtent, rx, ry) - start
    let radStart = ooStart / 180 * Math.PI
    let invStart = Math.atan2(rx * Math.sin(radStart), ry * Math.cos(radStart))
    let x0 = pt.x - rx * Math.cos(invStart) - rx
    let y0 = pt.y - ry * Math.sin(invStart) - ry
    let arc = { x: x0, y: y0, width: rx * 2, height: ry * 2, start: start, extent: extent, type: 0 }
    return arcToBezierCurvePaths(arc)
}

function arcToBezierCurvePaths(a) {
    let x, y, w, h, angStRad, increment, cv
    let arcSegs = 0, lineSegs = 0
    w = a.width / 2
    h = a.height / 2
    x = a.x + w
    y = a.y + h
    angStRad = -(a.start / 180 * Math.PI)
    let ext = -a.extent
    if (ext >= 360 || ext <= -360) {
        arcSegs = 4
        increment = Math.PI / 2
        cv = 0.5522847498307933
        if (ext < 0) {
            increment = -increment
            cv = -cv
        }
    } else {
        arcSegs = Math.ceil(Math.abs(ext) / 90)
        increment = (ext / arcSegs) / 180 * Math.PI
        let _increment = increment / 2
        cv = 4 / 3 * Math.sin(_increment) / (1 + Math.cos(_increment))
        if (cv == 0) {
            arcSegs = 0
        }
    }
    lineSegs = a.type
    if (w < 0 || h < 0) {
        arcSegs = lineSegs = -1
    }
    let paths = []
    let coords = [0, 0, 0, 0, 0, 0]
    for (let index = 0; index <= arcSegs + lineSegs; index++) {
        let angle = angStRad
        if (index == 0) {
            coords[0] = x + Math.cos(angle) * w
            coords[1] = y + Math.sin(angle) * h
            paths.push('M')
            paths.push(coords[0])
            paths.push(coords[1])
            continue
        }
        if (index > arcSegs) {
            if (index == arcSegs + lineSegs) {
                paths.push('Z')
                continue
            }
            coords[0] = x
            coords[1] = y
            paths.push('L')
            paths.push(coords[0])
            paths.push(coords[1])
            continue
        }
        angle += increment * (index - 1)
        let relx = Math.cos(angle)
        let rely = Math.sin(angle)
        coords[0] = x + (relx - cv * rely) * w
        coords[1] = y + (rely + cv * relx) * h
        angle += increment
        relx = Math.cos(angle)
        rely = Math.sin(angle)
        coords[2] = x + (relx + cv * rely) * w
        coords[3] = y + (rely - cv * relx) * h
        coords[4] = x + relx * w
        coords[5] = y + rely * h
        paths.push('C')
        paths.push(coords[0])
        paths.push(coords[1])
        paths.push(coords[2])
        paths.push(coords[3])
        paths.push(coords[4])
        paths.push(coords[5])
    }
    return paths
}

function convertOoxml2Angle(ooAngle, width, height) {
    let aspect = height / width
    let awtAngle = -ooAngle
    let awtAngle2 = awtAngle % 360
    let awtAngle3 = awtAngle - awtAngle2
    switch (parseInt(awtAngle2 / 90)) {
        case -3:
            awtAngle3 -= 360
            awtAngle2 += 360
            break
        case -2:
        case -1:
            awtAngle3 -= 180
            awtAngle2 += 180
        case 0:
            break
        default:
            break
        case 1:
        case 2:
            awtAngle3 += 180
            awtAngle2 -= 180
            break
        case 3:
            awtAngle3 += 360
            awtAngle2 -= 360
    }
    awtAngle = (Math.atan2(Math.tan(awtAngle2 / 180 * Math.PI), aspect)) * 180 / Math.PI + awtAngle3
    return awtAngle
}

function value2px(v, isEmu) {
    if (isEmu) {
        return +v / 12700
    } else {
        return +v
    }
}

function value2emu(v) {
    return +v * 12700
}

function valueEquals(v1, v2) {
    if (v1 == v2) {
        return true
    }
    if (v1 != null && v2 == null || v1 == null && v2 != null) {
        return false
    }
    if (v1 > 1000 && Math.abs(v1 - v2) < 0.001) {
        return true
    }
    let f1 = v1.toFixed(6)
    let f2 = v2.toFixed(6)
    if (f1 == f2) {
        return true
    }
    if (f1 == '0.000000' && f2 == '-0.000000' || f1 == '-0.000000' && f2 == '0.000000') {
        return true
    }
    return false
}

// export { geometryPaths }